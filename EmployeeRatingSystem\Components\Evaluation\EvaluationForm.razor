@page "/evaluation/{employeeId}/{month:int}/{year:int}"
@using EmployeeRatingSystem.Models
@using EmployeeRatingSystem.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.SignalR.Client
@using Microsoft.AspNetCore.Components.Authorization
@using System.Globalization
@inject EvaluationService EvaluationService
@inject EvaluationCalculationService CalculationService
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@implements IAsyncDisposable

@attribute [Authorize(Roles = "SuperAdmin,Manager,DirectSupervisor")]

<div class="evaluation-container" dir="@(IsArabic ? "rtl" : "ltr")">
    @if (evaluation != null)
    {
        <div class="evaluation-header">
            <h2 class="page-title">
                @if (IsArabic)
                {
                    <span class="ar-text">تقييم الموظف</span>
                    <small class="en-text">(Employee Evaluation)</small>
                }
                else
                {
                    <span class="en-text">Employee Evaluation</span>
                    <small class="ar-text">(تقييم الموظف)</small>
                }
            </h2>
            
            <div class="language-toggle">
                <button class="btn btn-outline-secondary btn-sm" @onclick="ToggleLanguage">
                    @(IsArabic ? "English" : "العربية")
                </button>
            </div>
        </div>

        <EditForm Model="evaluation" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />
            
            <!-- Section 1: Work Volume Metrics -->
            <div class="evaluation-section">
                <h3 class="section-title">
                    @if (IsArabic)
                    {
                        <span class="ar-text">القسم الأول: مقاييس حجم العمل</span>
                        <small class="en-text">(Section 1: Work Volume Metrics)</small>
                    }
                    else
                    {
                        <span class="en-text">Section 1: Work Volume Metrics</span>
                        <small class="ar-text">(القسم الأول: مقاييس حجم العمل)</small>
                    }
                </h3>

                <!-- Employee Information -->
                <div class="employee-info-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Employee Information", "معلومات الموظف")
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Employee Name", "اسم الموظف")</label>
                                <input type="text" class="form-control" value="@evaluation.Employee.ArabicName" readonly />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Employee ID", "الرقم الوظيفي")</label>
                                <input type="text" class="form-control" value="@evaluation.Employee.EmployeeId" readonly />
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Department", "القسم")</label>
                                <input type="text" class="form-control" value="@(evaluation.Employee.PrimaryDepartment?.NameAr ?? "N/A")" readonly />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Evaluation Period", "فترة التقييم")</label>
                                <input type="text" class="form-control" value="@($"{evaluation.EvaluationPeriodMonth}/{evaluation.EvaluationPeriodYear}")" readonly />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Work Volume Inputs -->
                <div class="work-volume-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Work Volume Inputs", "مدخلات حجم العمل")
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Quality Program Work", "حجم عمل الموظف في برنامج الجودة")</label>
                                <InputNumber class="form-control" @bind-Value="evaluation.QualityProgramWork" 
                                           @onchange="OnWorkVolumeChanged" step="0.01" />
                                <ValidationMessage For="@(() => evaluation.QualityProgramWork)" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Oracle Program Work", "حجم عمل الموظف في برنامج الأوراكل")</label>
                                <InputNumber class="form-control" @bind-Value="evaluation.OracleProgramWork" 
                                           @onchange="OnWorkVolumeChanged" step="0.01" />
                                <ValidationMessage For="@(() => evaluation.OracleProgramWork)" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Documented Work", "حجم عمل الموظف في أي عمل موثق")</label>
                                <InputNumber class="form-control" @bind-Value="evaluation.DocumentedWork" 
                                           @onchange="OnWorkVolumeChanged" step="0.01" />
                                <ValidationMessage For="@(() => evaluation.DocumentedWork)" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto-calculated Fields -->
                <div class="calculated-fields-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Auto-calculated Fields", "الحقول المحسوبة تلقائياً")
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Work Percentage in Department", "النسبة المئوية لحجم عمل الموظف داخل القسم")</label>
                                <input type="text" class="form-control calculated-field" 
                                       value="@evaluation.WorkPercentageInDepartment.ToString("P2")" readonly />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Score out of 60%", "الدرجة 60%")</label>
                                <input type="text" class="form-control calculated-field" 
                                       value="@evaluation.SixtyPercentScore.ToString("F4")" readonly />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Metrics -->
                <div class="attendance-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Attendance Metrics", "مقاييس الحضور")
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Monthly Attendance Days", "عدد أيام الحضور في الشهر")</label>
                                <InputNumber class="form-control" @bind-Value="evaluation.MonthlyAttendanceDays" 
                                           @onchange="OnAttendanceChanged" />
                                <ValidationMessage For="@(() => evaluation.MonthlyAttendanceDays)" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Employee Attendance Days", "عدد أيام حضور الموظف")</label>
                                <InputNumber class="form-control" @bind-Value="evaluation.EmployeeAttendanceDays" 
                                           @onchange="OnAttendanceChanged" />
                                <ValidationMessage For="@(() => evaluation.EmployeeAttendanceDays)" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Attendance Percentage", "نسبة حضور الموظف")</label>
                                <input type="text" class="form-control calculated-field" 
                                       value="@evaluation.AttendancePercentage.ToString("P2")" readonly />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("20% Attendance Score", "نسبة حضور الموظف في الشهر 20%")</label>
                                <input type="text" class="form-control calculated-field" 
                                       value="@evaluation.TwentyPercentAttendanceScore.ToString("F4")" readonly />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Manager Evaluation -->
            <div class="evaluation-section">
                <h3 class="section-title">
                    @if (IsArabic)
                    {
                        <span class="ar-text">القسم الثاني: تقييم المدير</span>
                        <small class="en-text">(Section 2: Manager Evaluation)</small>
                    }
                    else
                    {
                        <span class="en-text">Section 2: Manager Evaluation</span>
                        <small class="ar-text">(القسم الثاني: تقييم المدير)</small>
                    }
                </h3>

                <!-- Performance Criteria -->
                <div class="manager-evaluation-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Performance Criteria (1-5 Scale)", "معايير الأداء (مقياس من 1-5)")
                    </h4>

                    <div class="criteria-grid">
                        <!-- Work Efficiency and Quality -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Work efficiency and quality", "كفاءة العمل وجودته")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="workEfficiency" value="@rating"
                                               checked="@(evaluation.WorkEfficiencyQuality == rating)"
                                               @onchange="@(() => { evaluation.WorkEfficiencyQuality = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Leadership Ability -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Leadership ability in decision-making and instruction-giving", "القدرة على القيادة في اتخاذ القرارات وإعطاء التوجيهات")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="leadership" value="@rating"
                                               checked="@(evaluation.LeadershipAbility == rating)"
                                               @onchange="@(() => { evaluation.LeadershipAbility = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Planning, Development, and Creativity -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Planning, development, and creativity capabilities", "قدرات التخطيط والتطوير والإبداع")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="planning" value="@rating"
                                               checked="@(evaluation.PlanningDevelopmentCreativity == rating)"
                                               @onchange="@(() => { evaluation.PlanningDevelopmentCreativity = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Teamwork Participation -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Teamwork participation and motivation of colleagues", "المشاركة في العمل الجماعي وتحفيز الزملاء")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="teamwork" value="@rating"
                                               checked="@(evaluation.TeamworkParticipation == rating)"
                                               @onchange="@(() => { evaluation.TeamworkParticipation = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Responsibility Handling -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Responsibility handling and work pressure management", "تحمل المسؤولية والتعامل مع ضغط العمل")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="responsibility" value="@rating"
                                               checked="@(evaluation.ResponsibilityHandling == rating)"
                                               @onchange="@(() => { evaluation.ResponsibilityHandling = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Emergency Management -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Emergency situation management", "إدارة المواقف الطارئة")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="emergency" value="@rating"
                                               checked="@(evaluation.EmergencyManagement == rating)"
                                               @onchange="@(() => { evaluation.EmergencyManagement = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- General Behavior -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("General behavior and social relationships", "السلوك العام والعلاقات الاجتماعية")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="behavior" value="@rating"
                                               checked="@(evaluation.GeneralBehavior == rating)"
                                               @onchange="@(() => { evaluation.GeneralBehavior = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Supervisor Relationship -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Relationship with supervisors", "العلاقة مع الرؤساء")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="supervisorRel" value="@rating"
                                               checked="@(evaluation.SupervisorRelationship == rating)"
                                               @onchange="@(() => { evaluation.SupervisorRelationship = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Discipline Compliance -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Discipline and official working hours compliance", "الانضباط والالتزام بساعات العمل الرسمية")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="discipline" value="@rating"
                                               checked="@(evaluation.DisciplineCompliance == rating)"
                                               @onchange="@(() => { evaluation.DisciplineCompliance = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Work Development Ability -->
                        <div class="criteria-item">
                            <label class="form-label">@GetLocalizedText("Ability to develop work level", "القدرة على تطوير مستوى العمل")</label>
                            <div class="rating-scale">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    int rating = i;
                                    <label class="rating-option">
                                        <input type="radio" name="development" value="@rating"
                                               checked="@(evaluation.WorkDevelopmentAbility == rating)"
                                               @onchange="@(() => { evaluation.WorkDevelopmentAbility = rating; OnManagerEvaluationChanged(); })" />
                                        <span class="rating-number">@rating</span>
                                    </label>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manager Evaluation Totals -->
                <div class="manager-totals-card">
                    <h4 class="subsection-title">
                        @GetLocalizedText("Manager Evaluation Totals", "مجاميع تقييم المدير")
                    </h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Total Manager Score (out of 50)", "مجموع درجات تقييم المدير من 50")</label>
                                <input type="text" class="form-control calculated-field"
                                       value="@evaluation.TotalManagerScore.ToString("F2")" readonly />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">@GetLocalizedText("Manager Evaluation 20%", "تقييم المدير 20%")</label>
                                <input type="text" class="form-control calculated-field"
                                       value="@evaluation.TwentyPercentManagerScore.ToString("F4")" readonly />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Final Calculations -->
            <div class="evaluation-section">
                <h3 class="section-title">
                    @GetLocalizedText("Final Calculations", "الحسابات النهائية")
                </h3>

                <div class="final-calculations-card">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label total-score-label">@GetLocalizedText("Total Evaluation Score 100%", "مجموع درجات التقييم 100%")</label>
                                <input type="text" class="form-control total-score-field"
                                       value="@evaluation.TotalEvaluationScore.ToString("F4")" readonly />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes Section -->
            <div class="evaluation-section">
                <h3 class="section-title">
                    @GetLocalizedText("Notes", "الملاحظات")
                </h3>

                <div class="notes-card">
                    <div class="form-group">
                        <label class="form-label">@GetLocalizedText("Additional Notes", "ملاحظات إضافية")</label>
                        <InputTextArea class="form-control" @bind-Value="evaluation.Notes"
                                     rows="4" maxlength="1000"
                                     placeholder="@GetLocalizedText("Enter any additional notes or comments...", "أدخل أي ملاحظات أو تعليقات إضافية...")" />
                        <small class="form-text text-muted">
                            @GetLocalizedText("Maximum 1000 characters", "الحد الأقصى 1000 حرف")
                        </small>
                    </div>
                </div>
            </div>

            <!-- Auto-save indicator -->
            <div class="auto-save-indicator">
                @if (isAutoSaving)
                {
                    <span class="text-info">
                        <i class="fas fa-spinner fa-spin"></i>
                        @GetLocalizedText("Auto-saving...", "حفظ تلقائي...")
                    </span>
                }
                else if (lastAutoSaveTime.HasValue)
                {
                    <span class="text-success">
                        <i class="fas fa-check"></i>
                        @GetLocalizedText($"Last saved: {lastAutoSaveTime.Value:HH:mm:ss}", $"آخر حفظ: {lastAutoSaveTime.Value:HH:mm:ss}")
                    </span>
                }
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary" @onclick="SaveDraft">
                    @GetLocalizedText("Save Draft", "حفظ مسودة")
                </button>
                <button type="submit" class="btn btn-primary">
                    @GetLocalizedText("Submit Evaluation", "إرسال التقييم")
                </button>
                <button type="button" class="btn btn-outline-secondary" @onclick="Cancel">
                    @GetLocalizedText("Cancel", "إلغاء")
                </button>
            </div>
        </EditForm>
    }
    else
    {
        <div class="loading-spinner">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>@GetLocalizedText("Loading evaluation...", "جاري تحميل التقييم...")</p>
        </div>
    }
</div>
