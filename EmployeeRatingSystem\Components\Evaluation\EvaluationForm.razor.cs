using EmployeeRatingSystem.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.JSInterop;
using System.Timers;

namespace EmployeeRatingSystem.Components.Evaluation
{
    public partial class EvaluationForm : IAsyncDisposable
    {
        [Parameter] public string EmployeeId { get; set; } = string.Empty;
        [Parameter] public int Month { get; set; }
        [Parameter] public int Year { get; set; }

        private EmployeeEvaluation? evaluation;
        private ApplicationUser? currentUser;
        private HubConnection? hubConnection;
        private System.Timers.Timer? autoSaveTimer;
        private bool isAutoSaving = false;
        private DateTime? lastAutoSaveTime;
        private bool IsArabic = true; // Default to Arabic

        protected override async Task OnInitializedAsync()
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                currentUser = await UserManager.GetUserAsync(authState.User);
                if (currentUser != null)
                {
                    evaluation = await EvaluationService.GetOrCreateEvaluationAsync(EmployeeId, Month, Year, currentUser);
                    
                    if (evaluation != null)
                    {
                        await SetupSignalRConnection();
                        SetupAutoSaveTimer();
                    }
                }
            }
        }

        private async Task SetupSignalRConnection()
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl(Navigation.ToAbsoluteUri("/evaluationHub"))
                .Build();

            hubConnection.On<object>("ScoreUpdated", (data) =>
            {
                InvokeAsync(StateHasChanged);
            });

            hubConnection.On<object>("AutoSaveCompleted", (data) =>
            {
                isAutoSaving = false;
                lastAutoSaveTime = DateTime.Now;
                InvokeAsync(StateHasChanged);
            });

            await hubConnection.StartAsync();
            
            if (evaluation != null)
            {
                await hubConnection.SendAsync("JoinEvaluationGroup", evaluation.Id.ToString());
            }
        }

        private void SetupAutoSaveTimer()
        {
            autoSaveTimer = new System.Timers.Timer(30000); // 30 seconds
            autoSaveTimer.Elapsed += OnAutoSaveTimer;
            autoSaveTimer.AutoReset = true;
            autoSaveTimer.Enabled = true;
        }

        private async void OnAutoSaveTimer(object? sender, ElapsedEventArgs e)
        {
            if (evaluation != null && currentUser != null && !isAutoSaving)
            {
                await InvokeAsync(async () =>
                {
                    isAutoSaving = true;
                    StateHasChanged();
                    
                    var success = await EvaluationService.SaveEvaluationAsync(evaluation, currentUser);
                    
                    if (hubConnection != null)
                    {
                        await hubConnection.SendAsync("NotifyAutoSave", evaluation.Id.ToString(), success);
                    }
                    else
                    {
                        isAutoSaving = false;
                        lastAutoSaveTime = success ? DateTime.Now : null;
                        StateHasChanged();
                    }
                });
            }
        }

        private async Task OnWorkVolumeChanged()
        {
            if (evaluation != null)
            {
                await CalculationService.RecalculateAllScoresAsync(evaluation);
                
                if (hubConnection != null)
                {
                    await hubConnection.SendAsync("UpdateCalculation", 
                        evaluation.Id.ToString(), 
                        evaluation.SixtyPercentScore, 
                        "WorkVolume");
                }
                
                StateHasChanged();
            }
        }

        private async Task OnAttendanceChanged()
        {
            if (evaluation != null)
            {
                await CalculationService.RecalculateAllScoresAsync(evaluation);
                
                if (hubConnection != null)
                {
                    await hubConnection.SendAsync("UpdateCalculation", 
                        evaluation.Id.ToString(), 
                        evaluation.TwentyPercentAttendanceScore, 
                        "Attendance");
                }
                
                StateHasChanged();
            }
        }

        private async Task OnManagerEvaluationChanged()
        {
            if (evaluation != null)
            {
                await CalculationService.RecalculateAllScoresAsync(evaluation);
                
                if (hubConnection != null)
                {
                    await hubConnection.SendAsync("UpdateCalculation", 
                        evaluation.Id.ToString(), 
                        evaluation.TwentyPercentManagerScore, 
                        "ManagerEvaluation");
                }
                
                StateHasChanged();
            }
        }

        private async Task HandleValidSubmit()
        {
            if (evaluation != null && currentUser != null)
            {
                var success = await EvaluationService.SubmitEvaluationAsync(evaluation.Id, currentUser);
                if (success)
                {
                    Navigation.NavigateTo("/evaluations");
                }
                else
                {
                    // Show error message
                    await JSRuntime.InvokeVoidAsync("alert", GetLocalizedText("Failed to submit evaluation. Please check all required fields.", "فشل في إرسال التقييم. يرجى التحقق من جميع الحقول المطلوبة."));
                }
            }
        }

        private async Task SaveDraft()
        {
            if (evaluation != null && currentUser != null)
            {
                isAutoSaving = true;
                StateHasChanged();
                
                var success = await EvaluationService.SaveEvaluationAsync(evaluation, currentUser);
                
                isAutoSaving = false;
                lastAutoSaveTime = success ? DateTime.Now : null;
                StateHasChanged();
                
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", GetLocalizedText("Draft saved successfully.", "تم حفظ المسودة بنجاح."));
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", GetLocalizedText("Failed to save draft.", "فشل في حفظ المسودة."));
                }
            }
        }

        private void Cancel()
        {
            Navigation.NavigateTo("/evaluations");
        }

        private void ToggleLanguage()
        {
            IsArabic = !IsArabic;
            StateHasChanged();
        }

        private string GetLocalizedText(string english, string arabic)
        {
            return IsArabic ? arabic : english;
        }

        public async ValueTask DisposeAsync()
        {
            autoSaveTimer?.Dispose();
            
            if (hubConnection != null)
            {
                if (evaluation != null)
                {
                    await hubConnection.SendAsync("LeaveEvaluationGroup", evaluation.Id.ToString());
                }
                await hubConnection.DisposeAsync();
            }
        }
    }
}
