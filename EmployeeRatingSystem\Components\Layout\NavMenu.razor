﻿@using EmployeeRatingSystem.Models
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <span class="ar-text">نظام تقييم الموظفين</span>
            <small class="en-text">Employee Rating System</small>
        </a>

        @if (currentUser != null)
        {
            <div class="user-info">
                <span class="user-name">@currentUser.ArabicName</span>
                <button class="btn btn-outline-light btn-sm" @onclick="Logout">
                    <span class="ar-text">خروج</span>
                    <small class="en-text">(Logout)</small>
                </button>
            </div>
        }
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span>
                <span class="ar-text">الرئيسية</span>
                <small class="en-text">(Home)</small>
            </NavLink>
        </div>

        @if (currentUser?.Role == UserRole.SuperAdmin || currentUser?.Role == UserRole.Manager || currentUser?.Role == UserRole.DirectSupervisor)
        {
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="evaluations">
                    <span class="bi bi-clipboard-check-nav-menu" aria-hidden="true"></span>
                    <span class="ar-text">التقييمات</span>
                    <small class="en-text">(Evaluations)</small>
                </NavLink>
            </div>
        }

        @if (currentUser?.Role == UserRole.Employee)
        {
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="my-evaluations">
                    <span class="bi bi-person-check-nav-menu" aria-hidden="true"></span>
                    <span class="ar-text">تقييماتي</span>
                    <small class="en-text">(My Evaluations)</small>
                </NavLink>
            </div>
        }

        @if (currentUser?.Role == UserRole.ExcellenceTeam)
        {
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="excellence-dashboard">
                    <span class="bi bi-graph-up-nav-menu" aria-hidden="true"></span>
                    <span class="ar-text">لوحة التميز</span>
                    <small class="en-text">(Excellence Dashboard)</small>
                </NavLink>
            </div>
        }
    </nav>
</div>

<style>
    .navbar-brand {
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
    }

    .ar-text {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .en-text {
        font-size: 0.8rem;
        font-weight: 400;
        opacity: 0.8;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .user-name {
        color: white;
        font-size: 0.9rem;
    }

    .nav-link .ar-text {
        font-size: 1rem;
    }

    .nav-link .en-text {
        font-size: 0.75rem;
        display: block;
        margin-left: 20px;
    }
</style>

@code {
    private ApplicationUser? currentUser;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            currentUser = await UserManager.GetUserAsync(authState.User);
        }
    }

    private async Task Logout()
    {
        await SignInManager.SignOutAsync();
        Navigation.NavigateTo("/login");
    }
}

