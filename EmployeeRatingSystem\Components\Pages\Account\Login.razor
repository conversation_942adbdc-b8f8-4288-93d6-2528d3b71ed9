@page "/Account/Login"
@inject NavigationManager Navigation

@code {
    protected override void OnInitialized()
    {
        // Redirect to our custom login page
        var returnUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
        var queryString = Navigation.ToAbsoluteUri(Navigation.Uri).Query;
        
        if (queryString.Contains("ReturnUrl"))
        {
            Navigation.NavigateTo($"/login{queryString}");
        }
        else
        {
            Navigation.NavigateTo("/login");
        }
    }
}
