﻿@page "/"
@using EmployeeRatingSystem.Models
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation

@attribute [Authorize]

<PageTitle>Employee Rating System</PageTitle>

<div class="home-container">
    <div class="welcome-header">
        <h1 class="display-4">
            <span class="ar-text">نظام تقييم الموظفين</span><br>
            <small class="en-text">Employee Rating System</small>
        </h1>

        @if (currentUser != null)
        {
            <div class="user-welcome">
                <h3>
                    <span class="ar-text">مرحباً، @currentUser.ArabicName</span><br>
                    <small class="en-text">Welcome, @currentUser.EnglishName</small>
                </h3>
                <p class="user-role">
                    <span class="badge bg-primary">@currentUser.Role</span>
                </p>
            </div>
        }
    </div>

    <div class="dashboard-cards">
        @if (currentUser?.Role == UserRole.SuperAdmin || currentUser?.Role == UserRole.Manager || currentUser?.Role == UserRole.DirectSupervisor)
        {
            <div class="card action-card">
                <div class="card-body">
                    <h5 class="card-title">
                        <span class="ar-text">إنشاء تقييم جديد</span><br>
                        <small class="en-text">Create New Evaluation</small>
                    </h5>
                    <p class="card-text">
                        <span class="ar-text">قم بإنشاء تقييم جديد للموظفين</span><br>
                        <small class="en-text">Create a new employee evaluation</small>
                    </p>
                    <button class="btn btn-primary" @onclick="NavigateToEvaluation">
                        <span class="ar-text">إنشاء تقييم</span>
                        <small class="en-text">(Create Evaluation)</small>
                    </button>
                </div>
            </div>
        }

        <div class="card action-card">
            <div class="card-body">
                <h5 class="card-title">
                    <span class="ar-text">عرض التقييمات</span><br>
                    <small class="en-text">View Evaluations</small>
                </h5>
                <p class="card-text">
                    <span class="ar-text">عرض وإدارة التقييمات الحالية</span><br>
                    <small class="en-text">View and manage current evaluations</small>
                </p>
                <button class="btn btn-outline-primary" @onclick="NavigateToEvaluationsList">
                    <span class="ar-text">عرض التقييمات</span>
                    <small class="en-text">(View Evaluations)</small>
                </button>
            </div>
        </div>

        @if (currentUser?.Role == UserRole.Employee)
        {
            <div class="card action-card">
                <div class="card-body">
                    <h5 class="card-title">
                        <span class="ar-text">تقييماتي</span><br>
                        <small class="en-text">My Evaluations</small>
                    </h5>
                    <p class="card-text">
                        <span class="ar-text">عرض تقييماتي الشخصية</span><br>
                        <small class="en-text">View my personal evaluations</small>
                    </p>
                    <button class="btn btn-success" @onclick="NavigateToMyEvaluations">
                        <span class="ar-text">تقييماتي</span>
                        <small class="en-text">(My Evaluations)</small>
                    </button>
                </div>
            </div>
        }
    </div>

    <div class="quick-demo mt-4">
        <div class="alert alert-info">
            <h6>
                <span class="ar-text">تجربة سريعة</span>
                <small class="en-text">(Quick Demo)</small>
            </h6>
            <p>
                <span class="ar-text">لتجربة النظام، يمكنك إنشاء تقييم تجريبي للموظف:</span><br>
                <small class="en-text">To test the system, you can create a demo evaluation for employee:</small>
            </p>
            <button class="btn btn-sm btn-outline-info" @onclick="NavigateToDemoEvaluation">
                <span class="ar-text">تقييم تجريبي لفاطمة خالد</span>
                <small class="en-text">(Demo Evaluation for Fatima Khalid)</small>
            </button>
        </div>
    </div>
</div>

<style>
    .home-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
    }

    .welcome-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }

    .ar-text {
        font-size: 1.8rem;
        font-weight: 600;
    }

    .en-text {
        font-size: 1.2rem;
        font-weight: 400;
        opacity: 0.9;
    }

    .user-welcome {
        margin-top: 20px;
    }

    .user-role {
        margin-top: 10px;
    }

    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .action-card {
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease-in-out;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }

    .card-title .ar-text {
        font-size: 1.3rem;
        color: #2c3e50;
    }

    .card-text .ar-text {
        font-size: 1rem;
        color: #495057;
    }
</style>

@code {
    private ApplicationUser? currentUser;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            currentUser = await UserManager.GetUserAsync(authState.User);
        }
        else
        {
            Navigation.NavigateTo("/login");
        }
    }

    private void NavigateToEvaluation()
    {
        // Navigate to create new evaluation page
        Navigation.NavigateTo("/evaluations/create");
    }

    private void NavigateToEvaluationsList()
    {
        Navigation.NavigateTo("/evaluations");
    }

    private void NavigateToMyEvaluations()
    {
        Navigation.NavigateTo("/my-evaluations");
    }

    private void NavigateToDemoEvaluation()
    {
        // Navigate to demo evaluation (current month/year for employee EMP004)
        var currentMonth = DateTime.Now.Month;
        var currentYear = DateTime.Now.Year;
        Navigation.NavigateTo($"/evaluation/EMP004/{currentMonth}/{currentYear}");
    }
}
