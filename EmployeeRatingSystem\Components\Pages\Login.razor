@page "/login"
@using EmployeeRatingSystem.Models
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using System.ComponentModel.DataAnnotations
@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2 class="text-center">
                <span class="ar-text">نظام تقييم الموظفين</span><br>
                <small class="en-text">Employee Rating System</small>
            </h2>
        </div>

        <EditForm Model="loginModel" OnValidSubmit="HandleLogin">
            <DataAnnotationsValidator />
            
            <div class="form-group mb-3">
                <label class="form-label">
                    <span class="ar-text">البريد الإلكتروني</span>
                    <small class="en-text">(Email)</small>
                </label>
                <InputText class="form-control" @bind-Value="loginModel.Email" placeholder="<EMAIL>" />
                <ValidationMessage For="@(() => loginModel.Email)" />
            </div>

            <div class="form-group mb-3">
                <label class="form-label">
                    <span class="ar-text">كلمة المرور</span>
                    <small class="en-text">(Password)</small>
                </label>
                <InputText type="password" class="form-control" @bind-Value="loginModel.Password" placeholder="Password123!" />
                <ValidationMessage For="@(() => loginModel.Password)" />
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    @errorMessage
                </div>
            }

            <button type="submit" class="btn btn-primary w-100" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2"></span>
                }
                <span class="ar-text">تسجيل الدخول</span>
                <small class="en-text">(Login)</small>
            </button>
        </EditForm>

        <div class="demo-accounts mt-4">
            <h6 class="text-center">
                <span class="ar-text">حسابات تجريبية</span>
                <small class="en-text">(Demo Accounts)</small>
            </h6>
            <div class="demo-list">
                <small>
                    <strong>Super Admin:</strong> <EMAIL><br>
                    <strong>Manager:</strong> <EMAIL><br>
                    <strong>Supervisor:</strong> <EMAIL><br>
                    <strong>Employee:</strong> <EMAIL><br>
                    <strong>Excellence Team:</strong> <EMAIL><br>
                    <em>Password for all: Password123!</em>
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
    }

    .login-card {
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        width: 100%;
        max-width: 400px;
    }

    .login-header h2 {
        color: #2c3e50;
        margin-bottom: 30px;
    }

    .ar-text {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .en-text {
        font-size: 1rem;
        font-weight: 400;
        color: #6c757d;
    }

    .demo-accounts {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .demo-list {
        text-align: left;
        color: #495057;
    }
</style>

@code {
    private LoginModel loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = string.Empty;
        StateHasChanged();

        try
        {
            var result = await SignInManager.PasswordSignInAsync(
                loginModel.Email, 
                loginModel.Password, 
                isPersistent: false, 
                lockoutOnFailure: true);

            if (result.Succeeded)
            {
                Navigation.NavigateTo("/");
            }
            else if (result.IsLockedOut)
            {
                errorMessage = "Account is locked out. Please try again later. / الحساب مقفل. يرجى المحاولة لاحقاً.";
            }
            else
            {
                errorMessage = "Invalid email or password. / بريد إلكتروني أو كلمة مرور غير صحيحة.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Login failed: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }
}
