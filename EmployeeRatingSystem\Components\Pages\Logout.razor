@page "/logout"
@using EmployeeRatingSystem.Models
@using Microsoft.AspNetCore.Identity
@inject SignInManager<ApplicationUser> SignInManager
@inject NavigationManager Navigation

<div class="logout-container">
    <div class="logout-card">
        <h3 class="text-center">
            <span class="ar-text">جاري تسجيل الخروج...</span><br>
            <small class="en-text">Logging out...</small>
        </h3>
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>
</div>

<style>
    .logout-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
    }

    .logout-card {
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        text-align: center;
    }

    .ar-text {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .en-text {
        font-size: 1rem;
        font-weight: 400;
        color: #6c757d;
    }
</style>

@code {
    protected override async Task OnInitializedAsync()
    {
        await SignInManager.SignOutAsync();
        await Task.Delay(1000); // Brief delay for user experience
        Navigation.NavigateTo("/login");
    }
}
