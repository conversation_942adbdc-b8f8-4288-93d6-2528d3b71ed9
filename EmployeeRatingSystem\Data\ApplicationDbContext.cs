using EmployeeRatingSystem.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Department> Departments { get; set; }
        public DbSet<UserDepartment> UserDepartments { get; set; }
        public DbSet<EmployeeEvaluation> EmployeeEvaluations { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure ApplicationUser
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.HasIndex(e => e.EmployeeId).IsUnique();
                entity.Property(e => e.EmployeeId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ArabicName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.EnglishName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Role).HasConversion<int>();

                // Relationship with primary department
                entity.HasOne(e => e.PrimaryDepartment)
                    .WithMany(d => d.PrimaryEmployees)
                    .HasForeignKey(e => e.PrimaryDepartmentId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Department
            builder.Entity<Department>(entity =>
            {
                entity.HasIndex(e => e.Code).IsUnique();
                entity.Property(e => e.NameEn).IsRequired().HasMaxLength(255);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);

                // Self-referencing relationship for hierarchy
                entity.HasOne(e => e.Parent)
                    .WithMany(e => e.Children)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Many-to-many with managers
                entity.HasMany(d => d.Managers)
                    .WithMany(u => u.ManagedDepartments)
                    .UsingEntity<Dictionary<string, object>>(
                        "DepartmentManager",
                        j => j.HasOne<ApplicationUser>().WithMany().HasForeignKey("UserId"),
                        j => j.HasOne<Department>().WithMany().HasForeignKey("DepartmentId"));
            });

            // Configure UserDepartment
            builder.Entity<UserDepartment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RoleInDepartment).HasConversion<int>();

                entity.HasOne(e => e.User)
                    .WithMany(u => u.UserDepartments)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Department)
                    .WithMany(d => d.UserDepartments)
                    .HasForeignKey(e => e.DepartmentId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Ensure unique user-department combination
                entity.HasIndex(e => new { e.UserId, e.DepartmentId }).IsUnique();
            });

            // Configure EmployeeEvaluation
            builder.Entity<EmployeeEvaluation>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<int>();

                entity.HasOne(e => e.Employee)
                    .WithMany(u => u.EvaluationsAsEmployee)
                    .HasForeignKey(e => e.EmployeeId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Evaluator)
                    .WithMany(u => u.EvaluationsAsEvaluator)
                    .HasForeignKey(e => e.EvaluatorId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ApprovedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ApprovedById)
                    .OnDelete(DeleteBehavior.SetNull);

                // Ensure unique evaluation per employee per month/year
                entity.HasIndex(e => new { e.EmployeeId, e.EvaluationPeriodMonth, e.EvaluationPeriodYear })
                    .IsUnique();

                // Configure decimal precision
                entity.Property(e => e.QualityProgramWork).HasPrecision(18, 2);
                entity.Property(e => e.OracleProgramWork).HasPrecision(18, 2);
                entity.Property(e => e.DocumentedWork).HasPrecision(18, 2);
                entity.Property(e => e.WorkPercentageInDepartment).HasPrecision(18, 4);
                entity.Property(e => e.SixtyPercentScore).HasPrecision(18, 4);
                entity.Property(e => e.AttendancePercentage).HasPrecision(18, 4);
                entity.Property(e => e.TwentyPercentAttendanceScore).HasPrecision(18, 4);
                entity.Property(e => e.TotalManagerScore).HasPrecision(18, 2);
                entity.Property(e => e.TwentyPercentManagerScore).HasPrecision(18, 4);
                entity.Property(e => e.TotalEvaluationScore).HasPrecision(18, 4);
            });
        }
    }
}
