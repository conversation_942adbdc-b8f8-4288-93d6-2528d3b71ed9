using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace EmployeeRatingSystem.Hubs
{
    [Authorize]
    public class EvaluationHub : Hub
    {
        public async Task JoinEvaluationGroup(string evaluationId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"evaluation-{evaluationId}");
        }

        public async Task LeaveEvaluationGroup(string evaluationId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"evaluation-{evaluationId}");
        }

        public async Task UpdateCalculation(string evaluationId, decimal newScore, string calculationType)
        {
            await Clients.Group($"evaluation-{evaluationId}")
                .SendAsync("ScoreUpdated", new { evaluationId, newScore, calculationType, timestamp = DateTime.UtcNow });
        }

        public async Task NotifyAutoSave(string evaluationId, bool success)
        {
            await Clients.Group($"evaluation-{evaluationId}")
                .SendAsync("AutoSaveCompleted", new { evaluationId, success, timestamp = DateTime.UtcNow });
        }

        public async Task NotifyValidationError(string evaluationId, string fieldName, string errorMessage)
        {
            await Clients.Group($"evaluation-{evaluationId}")
                .SendAsync("ValidationError", new { evaluationId, fieldName, errorMessage, timestamp = DateTime.UtcNow });
        }

        public override async Task OnConnectedAsync()
        {
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            await base.OnDisconnectedAsync(exception);
        }
    }
}
