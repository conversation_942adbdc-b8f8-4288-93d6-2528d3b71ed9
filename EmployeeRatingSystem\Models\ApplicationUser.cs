using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(50)]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string ArabicName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string EnglishName { get; set; } = string.Empty;

        public int? PrimaryDepartmentId { get; set; }
        public Department? PrimaryDepartment { get; set; }

        public UserRole Role { get; set; } = UserRole.Employee;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public ICollection<UserDepartment> UserDepartments { get; set; } = new List<UserDepartment>();
        public ICollection<EmployeeEvaluation> EvaluationsAsEmployee { get; set; } = new List<EmployeeEvaluation>();
        public ICollection<EmployeeEvaluation> EvaluationsAsEvaluator { get; set; } = new List<EmployeeEvaluation>();
        public ICollection<Department> ManagedDepartments { get; set; } = new List<Department>();
    }

    public enum UserRole
    {
        SuperAdmin = 1,
        Manager = 2,
        DirectSupervisor = 3,
        ExcellenceTeam = 4,
        Employee = 5
    }
}
