using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Models
{
    public class Department
    {
        public int Id { get; set; }

        [Required]
        [StringLength(255)]
        public string NameEn { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;

        public int? ParentId { get; set; }
        public Department? Parent { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public ICollection<Department> Children { get; set; } = new List<Department>();
        public ICollection<ApplicationUser> Managers { get; set; } = new List<ApplicationUser>();
        public ICollection<ApplicationUser> PrimaryEmployees { get; set; } = new List<ApplicationUser>();
        public ICollection<UserDepartment> UserDepartments { get; set; } = new List<UserDepartment>();
    }
}
