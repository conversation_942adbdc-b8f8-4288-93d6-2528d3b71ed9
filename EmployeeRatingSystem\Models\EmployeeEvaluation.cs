using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Models
{
    public class EmployeeEvaluation
    {
        public int Id { get; set; }

        [Required]
        public string EmployeeId { get; set; } = string.Empty;
        public ApplicationUser Employee { get; set; } = null!;

        [Required]
        [Range(1, 12)]
        public int EvaluationPeriodMonth { get; set; }

        [Required]
        [Range(2020, 2100)]
        public int EvaluationPeriodYear { get; set; }

        // Section 1: Work Volume Metrics
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, 999999.99)]
        public decimal QualityProgramWork { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Range(0, 999999.99)]
        public decimal OracleProgramWork { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Range(0, 999999.99)]
        public decimal DocumentedWork { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal WorkPercentageInDepartment { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal SixtyPercentScore { get; set; }

        // Attendance Metrics
        [Range(1, 31)]
        public int MonthlyAttendanceDays { get; set; }

        [Range(0, 31)]
        public int EmployeeAttendanceDays { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal AttendancePercentage { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal TwentyPercentAttendanceScore { get; set; }

        // Section 2: Manager Evaluation Criteria (1-5 scale each)
        [Range(1, 5)]
        public int WorkEfficiencyQuality { get; set; }

        [Range(1, 5)]
        public int LeadershipAbility { get; set; }

        [Range(1, 5)]
        public int PlanningDevelopmentCreativity { get; set; }

        [Range(1, 5)]
        public int TeamworkParticipation { get; set; }

        [Range(1, 5)]
        public int ResponsibilityHandling { get; set; }

        [Range(1, 5)]
        public int EmergencyManagement { get; set; }

        [Range(1, 5)]
        public int GeneralBehavior { get; set; }

        [Range(1, 5)]
        public int SupervisorRelationship { get; set; }

        [Range(1, 5)]
        public int DisciplineCompliance { get; set; }

        [Range(1, 5)]
        public int WorkDevelopmentAbility { get; set; }

        // Calculated Manager Evaluation Totals
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalManagerScore { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal TwentyPercentManagerScore { get; set; }

        // Final Calculations
        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalEvaluationScore { get; set; }

        // Notes Section
        [StringLength(1000)]
        public string? Notes { get; set; }

        // Evaluation Status and Metadata
        public EvaluationStatus Status { get; set; } = EvaluationStatus.Draft;

        [Required]
        public string EvaluatorId { get; set; } = string.Empty;
        public ApplicationUser Evaluator { get; set; } = null!;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? SubmittedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }

        public string? ApprovedById { get; set; }
        public ApplicationUser? ApprovedBy { get; set; }
    }

    public enum EvaluationStatus
    {
        Draft = 1,
        Submitted = 2,
        UnderReview = 3,
        Approved = 4,
        Rejected = 5
    }
}
