using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Models
{
    public class UserDepartment
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;
        public ApplicationUser User { get; set; } = null!;

        public int DepartmentId { get; set; }
        public Department Department { get; set; } = null!;

        public bool IsPrimary { get; set; } = false;

        public DepartmentRole RoleInDepartment { get; set; } = DepartmentRole.Employee;

        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
    }

    public enum DepartmentRole
    {
        Employee = 1,
        Supervisor = 2,
        Manager = 3
    }
}
