using EmployeeRatingSystem.Data;
using EmployeeRatingSystem.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Services
{
    public class DataSeedingService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;

        public DataSeedingService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
        }

        public async Task SeedDataAsync()
        {
            // Seed Roles
            await SeedRolesAsync();

            // Seed Departments
            await SeedDepartmentsAsync();

            // Seed Users
            await SeedUsersAsync();

            // Seed Sample Evaluations
            await SeedSampleEvaluationsAsync();
        }

        private async Task SeedRolesAsync()
        {
            var roles = new[]
            {
                "SuperAdmin",
                "Manager",
                "DirectSupervisor",
                "ExcellenceTeam",
                "Employee"
            };

            foreach (var role in roles)
            {
                if (!await _roleManager.RoleExistsAsync(role))
                {
                    await _roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private async Task SeedDepartmentsAsync()
        {
            if (await _context.Departments.AnyAsync())
                return;

            var departments = new[]
            {
                new Department
                {
                    Code = "IT",
                    NameEn = "Information Technology",
                    NameAr = "تقنية المعلومات",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Department
                {
                    Code = "HR",
                    NameEn = "Human Resources",
                    NameAr = "الموارد البشرية",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Department
                {
                    Code = "FIN",
                    NameEn = "Finance",
                    NameAr = "المالية",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Department
                {
                    Code = "QA",
                    NameEn = "Quality Assurance",
                    NameAr = "ضمان الجودة",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                }
            };

            _context.Departments.AddRange(departments);
            await _context.SaveChangesAsync();
        }

        private async Task SeedUsersAsync()
        {
            if (await _userManager.Users.AnyAsync())
                return;

            var itDepartment = await _context.Departments.FirstAsync(d => d.Code == "IT");
            var hrDepartment = await _context.Departments.FirstAsync(d => d.Code == "HR");
            var finDepartment = await _context.Departments.FirstAsync(d => d.Code == "FIN");
            var qaDepartment = await _context.Departments.FirstAsync(d => d.Code == "QA");

            var users = new[]
            {
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP001",
                    ArabicName = "أحمد محمد الإداري",
                    EnglishName = "Ahmed Mohamed Admin",
                    Role = UserRole.SuperAdmin,
                    PrimaryDepartmentId = itDepartment.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = true
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP002",
                    ArabicName = "سارة أحمد المدير",
                    EnglishName = "Sara Ahmed Manager",
                    Role = UserRole.Manager,
                    PrimaryDepartmentId = itDepartment.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = true
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP003",
                    ArabicName = "محمد علي المشرف",
                    EnglishName = "Mohamed Ali Supervisor",
                    Role = UserRole.DirectSupervisor,
                    PrimaryDepartmentId = itDepartment.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = true
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP004",
                    ArabicName = "فاطمة خالد الموظف",
                    EnglishName = "Fatima Khalid Employee",
                    Role = UserRole.Employee,
                    PrimaryDepartmentId = itDepartment.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = true
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP005",
                    ArabicName = "عبدالله سالم التميز",
                    EnglishName = "Abdullah Salem Excellence",
                    Role = UserRole.ExcellenceTeam,
                    PrimaryDepartmentId = qaDepartment.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = true
                }
            };

            foreach (var user in users)
            {
                var result = await _userManager.CreateAsync(user, "Password123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, user.Role.ToString());
                }
            }
        }

        private async Task SeedSampleEvaluationsAsync()
        {
            if (await _context.EmployeeEvaluations.AnyAsync())
                return;

            var employee = await _userManager.Users.FirstAsync(u => u.Role == UserRole.Employee);
            var supervisor = await _userManager.Users.FirstAsync(u => u.Role == UserRole.DirectSupervisor);

            var sampleEvaluation = new EmployeeEvaluation
            {
                EmployeeId = employee.Id,
                Employee = employee,
                EvaluatorId = supervisor.Id,
                Evaluator = supervisor,
                EvaluationPeriodMonth = DateTime.Now.Month,
                EvaluationPeriodYear = DateTime.Now.Year,
                QualityProgramWork = 85.5m,
                OracleProgramWork = 92.0m,
                DocumentedWork = 78.5m,
                MonthlyAttendanceDays = 22,
                EmployeeAttendanceDays = 20,
                WorkEfficiencyQuality = 4,
                LeadershipAbility = 3,
                PlanningDevelopmentCreativity = 4,
                TeamworkParticipation = 5,
                ResponsibilityHandling = 4,
                EmergencyManagement = 3,
                GeneralBehavior = 5,
                SupervisorRelationship = 4,
                DisciplineCompliance = 5,
                WorkDevelopmentAbility = 4,
                Status = EvaluationStatus.Draft,
                Notes = "Sample evaluation for testing purposes. الموظف يظهر أداءً جيداً في معظم المجالات.",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.EmployeeEvaluations.Add(sampleEvaluation);
            await _context.SaveChangesAsync();
        }
    }
}
