using EmployeeRatingSystem.Data;
using EmployeeRatingSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Services
{
    public class DepartmentAuthorizationService
    {
        private readonly ApplicationDbContext _context;

        public DepartmentAuthorizationService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<bool> HasDepartmentAccessAsync(ApplicationUser user, Department department)
        {
            return user.Role switch
            {
                UserRole.SuperAdmin => true,
                UserRole.Manager => await IsManagerOfDepartmentAsync(user.Id, department.Id),
                UserRole.DirectSupervisor => department.Id == user.PrimaryDepartmentId,
                UserRole.ExcellenceTeam => true, // Read-only access
                UserRole.Employee => false, // No department access
                _ => false
            };
        }

        public async Task<bool> CanEvaluateUserAsync(ApplicationUser evaluator, ApplicationUser targetUser)
        {
            return evaluator.Role switch
            {
                UserRole.SuperAdmin => true,
                UserRole.Manager => await Is<PERSON>anagerOfEmployeeAsync(evaluator.Id, targetUser.Id),
                UserRole.DirectSupervisor => await IsDirectSupervisorOfEmployeeAsync(evaluator.Id, targetUser.Id),
                UserRole.ExcellenceTeam => false, // Read-only access, no evaluation rights
                UserRole.Employee => false, // No evaluation rights
                _ => false
            };
        }

        public async Task<bool> CanViewEvaluationAsync(ApplicationUser user, EmployeeEvaluation evaluation)
        {
            return user.Role switch
            {
                UserRole.SuperAdmin => true,
                UserRole.Manager => await IsManagerOfEmployeeAsync(user.Id, evaluation.EmployeeId),
                UserRole.DirectSupervisor => await IsDirectSupervisorOfEmployeeAsync(user.Id, evaluation.EmployeeId) || 
                                           evaluation.EvaluatorId == user.Id,
                UserRole.ExcellenceTeam => true, // Read-only access
                UserRole.Employee => evaluation.EmployeeId == user.Id, // Can only view own evaluations
                _ => false
            };
        }

        public async Task<bool> CanEditEvaluationAsync(ApplicationUser user, EmployeeEvaluation evaluation)
        {
            // Only allow editing of draft evaluations
            if (evaluation.Status != EvaluationStatus.Draft)
                return false;

            return user.Role switch
            {
                UserRole.SuperAdmin => true,
                UserRole.Manager => await IsManagerOfEmployeeAsync(user.Id, evaluation.EmployeeId),
                UserRole.DirectSupervisor => evaluation.EvaluatorId == user.Id && 
                                           await IsDirectSupervisorOfEmployeeAsync(user.Id, evaluation.EmployeeId),
                _ => false
            };
        }

        public async Task<List<Department>> GetAccessibleDepartmentsAsync(ApplicationUser user)
        {
            return user.Role switch
            {
                UserRole.SuperAdmin => await _context.Departments.Where(d => d.IsActive).ToListAsync(),
                UserRole.Manager => await GetManagedDepartmentHierarchyAsync(user.Id),
                UserRole.DirectSupervisor => user.PrimaryDepartmentId.HasValue 
                    ? await _context.Departments.Where(d => d.Id == user.PrimaryDepartmentId).ToListAsync()
                    : new List<Department>(),
                UserRole.ExcellenceTeam => await _context.Departments.Where(d => d.IsActive).ToListAsync(),
                UserRole.Employee => new List<Department>(),
                _ => new List<Department>()
            };
        }

        public async Task<List<ApplicationUser>> GetAccessibleEmployeesAsync(ApplicationUser user)
        {
            return user.Role switch
            {
                UserRole.SuperAdmin => await _context.Users.Where(u => u.IsActive).ToListAsync(),
                UserRole.Manager => await GetEmployeesInManagedHierarchyAsync(user.Id),
                UserRole.DirectSupervisor => await GetDirectReportsAsync(user.Id),
                UserRole.ExcellenceTeam => new List<ApplicationUser>(), // Read-only, no direct employee access
                UserRole.Employee => new List<ApplicationUser> { user }, // Only self
                _ => new List<ApplicationUser>()
            };
        }

        private async Task<bool> IsManagerOfDepartmentAsync(string managerId, int departmentId)
        {
            var managedDepartments = await GetManagedDepartmentHierarchyAsync(managerId);
            return managedDepartments.Any(d => d.Id == departmentId);
        }

        private async Task<bool> IsManagerOfEmployeeAsync(string managerId, string employeeId)
        {
            var employee = await _context.Users
                .Include(u => u.PrimaryDepartment)
                .FirstOrDefaultAsync(u => u.Id == employeeId);

            if (employee?.PrimaryDepartmentId == null)
                return false;

            return await IsManagerOfDepartmentAsync(managerId, employee.PrimaryDepartmentId.Value);
        }

        private async Task<bool> IsDirectSupervisorOfEmployeeAsync(string supervisorId, string employeeId)
        {
            var supervisor = await _context.Users.FindAsync(supervisorId);
            var employee = await _context.Users.FindAsync(employeeId);

            if (supervisor?.PrimaryDepartmentId == null || employee?.PrimaryDepartmentId == null)
                return false;

            // Check if supervisor is assigned as supervisor in the employee's department
            var userDepartment = await _context.UserDepartments
                .FirstOrDefaultAsync(ud => ud.UserId == supervisorId &&
                                          ud.DepartmentId == employee.PrimaryDepartmentId &&
                                          ud.RoleInDepartment == DepartmentRole.Supervisor &&
                                          ud.IsActive);

            return userDepartment != null;
        }

        private async Task<List<Department>> GetManagedDepartmentHierarchyAsync(string managerId)
        {
            var manager = await _context.Users
                .Include(u => u.ManagedDepartments)
                .FirstOrDefaultAsync(u => u.Id == managerId);

            if (manager == null)
                return new List<Department>();

            var managedDepartmentIds = manager.ManagedDepartments.Select(d => d.Id).ToList();
            var allDepartments = await _context.Departments.ToListAsync();

            var accessibleDepartments = new List<Department>();
            
            foreach (var departmentId in managedDepartmentIds)
            {
                accessibleDepartments.AddRange(GetDepartmentHierarchy(allDepartments, departmentId));
            }

            return accessibleDepartments.Distinct().ToList();
        }

        private List<Department> GetDepartmentHierarchy(List<Department> allDepartments, int rootDepartmentId)
        {
            var result = new List<Department>();
            var rootDepartment = allDepartments.FirstOrDefault(d => d.Id == rootDepartmentId);
            
            if (rootDepartment != null)
            {
                result.Add(rootDepartment);
                var children = allDepartments.Where(d => d.ParentId == rootDepartmentId).ToList();
                
                foreach (var child in children)
                {
                    result.AddRange(GetDepartmentHierarchy(allDepartments, child.Id));
                }
            }

            return result;
        }

        private async Task<List<ApplicationUser>> GetEmployeesInManagedHierarchyAsync(string managerId)
        {
            var accessibleDepartments = await GetManagedDepartmentHierarchyAsync(managerId);
            var departmentIds = accessibleDepartments.Select(d => d.Id).ToList();

            return await _context.Users
                .Where(u => u.PrimaryDepartmentId.HasValue && 
                           departmentIds.Contains(u.PrimaryDepartmentId.Value) &&
                           u.IsActive)
                .ToListAsync();
        }

        private async Task<List<ApplicationUser>> GetDirectReportsAsync(string supervisorId)
        {
            var supervisor = await _context.Users.FindAsync(supervisorId);
            
            if (supervisor?.PrimaryDepartmentId == null)
                return new List<ApplicationUser>();

            // Get employees in the same department where supervisor has supervisor role
            var supervisorDepartment = await _context.UserDepartments
                .FirstOrDefaultAsync(ud => ud.UserId == supervisorId &&
                                          ud.RoleInDepartment == DepartmentRole.Supervisor &&
                                          ud.IsActive);

            if (supervisorDepartment == null)
                return new List<ApplicationUser>();

            return await _context.Users
                .Where(u => u.PrimaryDepartmentId == supervisorDepartment.DepartmentId &&
                           u.Role == UserRole.Employee &&
                           u.IsActive)
                .ToListAsync();
        }
    }
}
