using EmployeeRatingSystem.Data;
using EmployeeRatingSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Services
{
    public class EvaluationCalculationService
    {
        private readonly ApplicationDbContext _context;

        public EvaluationCalculationService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Formula 1: Department work percentage calculation
        /// </summary>
        public async Task<decimal> CalculateWorkPercentageAsync(EmployeeEvaluation evaluation)
        {
            var employeeTotal = evaluation.QualityProgramWork + evaluation.OracleProgramWork + evaluation.DocumentedWork;
            
            if (employeeTotal == 0)
                return 0;

            var departmentTotals = await GetDepartmentTotalsAsync(
                evaluation.Employee.PrimaryDepartmentId ?? 0, 
                evaluation.EvaluationPeriodMonth, 
                evaluation.EvaluationPeriodYear);

            if (departmentTotals.TotalWork == 0)
                return 0;

            return employeeTotal / departmentTotals.TotalWork;
        }

        /// <summary>
        /// Formula 2: 60% Work Score calculation
        /// </summary>
        public async Task<decimal> CalculateSixtyPercentScoreAsync(EmployeeEvaluation evaluation)
        {
            var workPercentage = await CalculateWorkPercentageAsync(evaluation);
            
            if (workPercentage == 0)
                return 0;

            var highestPercentage = await GetHighestWorkPercentageInDepartmentAsync(
                evaluation.Employee.PrimaryDepartmentId ?? 0,
                evaluation.EvaluationPeriodMonth,
                evaluation.EvaluationPeriodYear);

            if (highestPercentage == 0)
                return 0;

            return (workPercentage / highestPercentage) * 0.6m;
        }

        /// <summary>
        /// Formula 3: Employee attendance rate calculation
        /// </summary>
        public decimal CalculateAttendancePercentage(EmployeeEvaluation evaluation)
        {
            if (evaluation.MonthlyAttendanceDays == 0)
                return 0;

            return (decimal)evaluation.EmployeeAttendanceDays / evaluation.MonthlyAttendanceDays;
        }

        /// <summary>
        /// Formula 4: 20% Attendance score calculation
        /// </summary>
        public async Task<decimal> CalculateTwentyPercentAttendanceScoreAsync(EmployeeEvaluation evaluation)
        {
            var attendancePercentage = CalculateAttendancePercentage(evaluation);
            
            if (attendancePercentage == 0)
                return 0;

            var highestAttendanceRate = await GetHighestAttendanceRateInDepartmentAsync(
                evaluation.Employee.PrimaryDepartmentId ?? 0,
                evaluation.EvaluationPeriodMonth,
                evaluation.EvaluationPeriodYear);

            if (highestAttendanceRate == 0)
                return 0;

            return (attendancePercentage / highestAttendanceRate) * 0.2m;
        }

        /// <summary>
        /// Formula 5: Manager evaluation score calculation
        /// </summary>
        public decimal CalculateTotalManagerScore(EmployeeEvaluation evaluation)
        {
            return evaluation.WorkEfficiencyQuality +
                   evaluation.LeadershipAbility +
                   evaluation.PlanningDevelopmentCreativity +
                   evaluation.TeamworkParticipation +
                   evaluation.ResponsibilityHandling +
                   evaluation.EmergencyManagement +
                   evaluation.GeneralBehavior +
                   evaluation.SupervisorRelationship +
                   evaluation.DisciplineCompliance +
                   evaluation.WorkDevelopmentAbility;
        }

        /// <summary>
        /// Formula 6: 20% Manager score calculation
        /// </summary>
        public decimal CalculateTwentyPercentManagerScore(EmployeeEvaluation evaluation)
        {
            var totalManagerScore = CalculateTotalManagerScore(evaluation);
            return (totalManagerScore / 50m) * 0.2m;
        }

        /// <summary>
        /// Formula 7: Total evaluation calculation
        /// </summary>
        public async Task<decimal> CalculateTotalEvaluationScoreAsync(EmployeeEvaluation evaluation)
        {
            var sixtyPercentScore = await CalculateSixtyPercentScoreAsync(evaluation);
            var twentyPercentAttendanceScore = await CalculateTwentyPercentAttendanceScoreAsync(evaluation);
            var twentyPercentManagerScore = CalculateTwentyPercentManagerScore(evaluation);

            return sixtyPercentScore + twentyPercentAttendanceScore + twentyPercentManagerScore;
        }

        /// <summary>
        /// Recalculate all scores for an evaluation
        /// </summary>
        public async Task RecalculateAllScoresAsync(EmployeeEvaluation evaluation)
        {
            evaluation.WorkPercentageInDepartment = await CalculateWorkPercentageAsync(evaluation);
            evaluation.SixtyPercentScore = await CalculateSixtyPercentScoreAsync(evaluation);
            evaluation.AttendancePercentage = CalculateAttendancePercentage(evaluation);
            evaluation.TwentyPercentAttendanceScore = await CalculateTwentyPercentAttendanceScoreAsync(evaluation);
            evaluation.TotalManagerScore = CalculateTotalManagerScore(evaluation);
            evaluation.TwentyPercentManagerScore = CalculateTwentyPercentManagerScore(evaluation);
            evaluation.TotalEvaluationScore = await CalculateTotalEvaluationScoreAsync(evaluation);
            evaluation.UpdatedAt = DateTime.UtcNow;
        }

        private async Task<(decimal TotalWork, decimal TotalQuality, decimal TotalOracle, decimal TotalDocumented)> 
            GetDepartmentTotalsAsync(int departmentId, int month, int year)
        {
            var evaluations = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Where(e => e.Employee.PrimaryDepartmentId == departmentId &&
                           e.EvaluationPeriodMonth == month &&
                           e.EvaluationPeriodYear == year &&
                           e.Status != EvaluationStatus.Draft)
                .ToListAsync();

            var totalQuality = evaluations.Sum(e => e.QualityProgramWork);
            var totalOracle = evaluations.Sum(e => e.OracleProgramWork);
            var totalDocumented = evaluations.Sum(e => e.DocumentedWork);
            var totalWork = totalQuality + totalOracle + totalDocumented;

            return (totalWork, totalQuality, totalOracle, totalDocumented);
        }

        private async Task<decimal> GetHighestWorkPercentageInDepartmentAsync(int departmentId, int month, int year)
        {
            var evaluations = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Where(e => e.Employee.PrimaryDepartmentId == departmentId &&
                           e.EvaluationPeriodMonth == month &&
                           e.EvaluationPeriodYear == year &&
                           e.Status != EvaluationStatus.Draft)
                .ToListAsync();

            if (!evaluations.Any())
                return 1; // Default to 1 to avoid division by zero

            return evaluations.Max(e => e.WorkPercentageInDepartment);
        }

        private async Task<decimal> GetHighestAttendanceRateInDepartmentAsync(int departmentId, int month, int year)
        {
            var evaluations = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Where(e => e.Employee.PrimaryDepartmentId == departmentId &&
                           e.EvaluationPeriodMonth == month &&
                           e.EvaluationPeriodYear == year &&
                           e.Status != EvaluationStatus.Draft)
                .ToListAsync();

            if (!evaluations.Any())
                return 1; // Default to 1 to avoid division by zero

            return evaluations.Max(e => e.AttendancePercentage);
        }
    }
}
