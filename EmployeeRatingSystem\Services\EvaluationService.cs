using EmployeeRatingSystem.Data;
using EmployeeRatingSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Services
{
    public class EvaluationService
    {
        private readonly ApplicationDbContext _context;
        private readonly EvaluationCalculationService _calculationService;
        private readonly DepartmentAuthorizationService _authService;

        public EvaluationService(
            ApplicationDbContext context,
            EvaluationCalculationService calculationService,
            DepartmentAuthorizationService authService)
        {
            _context = context;
            _calculationService = calculationService;
            _authService = authService;
        }

        public async Task<EmployeeEvaluation?> GetEvaluationAsync(int id, ApplicationUser currentUser)
        {
            var evaluation = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Include(e => e.Evaluator)
                .Include(e => e.ApprovedBy)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (evaluation == null)
                return null;

            var canView = await _authService.CanViewEvaluationAsync(currentUser, evaluation);
            return canView ? evaluation : null;
        }

        public async Task<EmployeeEvaluation?> GetOrCreateEvaluationAsync(
            string employeeId, 
            int month, 
            int year, 
            ApplicationUser evaluator)
        {
            var employee = await _context.Users.FindAsync(employeeId);
            if (employee == null)
                return null;

            var canEvaluate = await _authService.CanEvaluateUserAsync(evaluator, employee);
            if (!canEvaluate)
                return null;

            var evaluation = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Include(e => e.Evaluator)
                .FirstOrDefaultAsync(e => e.EmployeeId == employeeId &&
                                        e.EvaluationPeriodMonth == month &&
                                        e.EvaluationPeriodYear == year);

            if (evaluation == null)
            {
                evaluation = new EmployeeEvaluation
                {
                    EmployeeId = employeeId,
                    Employee = employee,
                    EvaluationPeriodMonth = month,
                    EvaluationPeriodYear = year,
                    EvaluatorId = evaluator.Id,
                    Evaluator = evaluator,
                    Status = EvaluationStatus.Draft,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.EmployeeEvaluations.Add(evaluation);
                await _context.SaveChangesAsync();
            }

            return evaluation;
        }

        public async Task<bool> SaveEvaluationAsync(EmployeeEvaluation evaluation, ApplicationUser currentUser)
        {
            var canEdit = await _authService.CanEditEvaluationAsync(currentUser, evaluation);
            if (!canEdit)
                return false;

            try
            {
                // Recalculate all scores
                await _calculationService.RecalculateAllScoresAsync(evaluation);

                _context.EmployeeEvaluations.Update(evaluation);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SubmitEvaluationAsync(int evaluationId, ApplicationUser currentUser)
        {
            var evaluation = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .FirstOrDefaultAsync(e => e.Id == evaluationId);

            if (evaluation == null)
                return false;

            var canEdit = await _authService.CanEditEvaluationAsync(currentUser, evaluation);
            if (!canEdit || evaluation.Status != EvaluationStatus.Draft)
                return false;

            // Validate required fields
            if (!IsEvaluationComplete(evaluation))
                return false;

            try
            {
                // Final calculation before submission
                await _calculationService.RecalculateAllScoresAsync(evaluation);

                evaluation.Status = EvaluationStatus.Submitted;
                evaluation.SubmittedAt = DateTime.UtcNow;
                evaluation.UpdatedAt = DateTime.UtcNow;

                _context.EmployeeEvaluations.Update(evaluation);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ApproveEvaluationAsync(int evaluationId, ApplicationUser approver)
        {
            var evaluation = await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .FirstOrDefaultAsync(e => e.Id == evaluationId);

            if (evaluation == null || evaluation.Status != EvaluationStatus.Submitted)
                return false;

            // Only managers can approve evaluations
            if (approver.Role != UserRole.Manager && approver.Role != UserRole.SuperAdmin)
                return false;

            var canApprove = await _authService.IsManagerOfEmployeeAsync(approver.Id, evaluation.EmployeeId);
            if (!canApprove && approver.Role != UserRole.SuperAdmin)
                return false;

            try
            {
                evaluation.Status = EvaluationStatus.Approved;
                evaluation.ApprovedAt = DateTime.UtcNow;
                evaluation.ApprovedById = approver.Id;
                evaluation.UpdatedAt = DateTime.UtcNow;

                _context.EmployeeEvaluations.Update(evaluation);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<EmployeeEvaluation>> GetEvaluationsForUserAsync(ApplicationUser user)
        {
            var accessibleEmployees = await _authService.GetAccessibleEmployeesAsync(user);
            var employeeIds = accessibleEmployees.Select(e => e.Id).ToList();

            return await _context.EmployeeEvaluations
                .Include(e => e.Employee)
                .Include(e => e.Evaluator)
                .Where(e => employeeIds.Contains(e.EmployeeId))
                .OrderByDescending(e => e.UpdatedAt)
                .ToListAsync();
        }

        public async Task<List<EmployeeEvaluation>> GetPendingEvaluationsAsync(ApplicationUser user)
        {
            var evaluations = await GetEvaluationsForUserAsync(user);
            
            return user.Role switch
            {
                UserRole.Manager or UserRole.SuperAdmin => evaluations
                    .Where(e => e.Status == EvaluationStatus.Submitted)
                    .ToList(),
                UserRole.DirectSupervisor => evaluations
                    .Where(e => e.Status == EvaluationStatus.Draft && e.EvaluatorId == user.Id)
                    .ToList(),
                _ => new List<EmployeeEvaluation>()
            };
        }

        private bool IsEvaluationComplete(EmployeeEvaluation evaluation)
        {
            // Check if all required fields are filled
            return evaluation.QualityProgramWork >= 0 &&
                   evaluation.OracleProgramWork >= 0 &&
                   evaluation.DocumentedWork >= 0 &&
                   evaluation.MonthlyAttendanceDays > 0 &&
                   evaluation.EmployeeAttendanceDays >= 0 &&
                   evaluation.WorkEfficiencyQuality >= 1 && evaluation.WorkEfficiencyQuality <= 5 &&
                   evaluation.LeadershipAbility >= 1 && evaluation.LeadershipAbility <= 5 &&
                   evaluation.PlanningDevelopmentCreativity >= 1 && evaluation.PlanningDevelopmentCreativity <= 5 &&
                   evaluation.TeamworkParticipation >= 1 && evaluation.TeamworkParticipation <= 5 &&
                   evaluation.ResponsibilityHandling >= 1 && evaluation.ResponsibilityHandling <= 5 &&
                   evaluation.EmergencyManagement >= 1 && evaluation.EmergencyManagement <= 5 &&
                   evaluation.GeneralBehavior >= 1 && evaluation.GeneralBehavior <= 5 &&
                   evaluation.SupervisorRelationship >= 1 && evaluation.SupervisorRelationship <= 5 &&
                   evaluation.DisciplineCompliance >= 1 && evaluation.DisciplineCompliance <= 5 &&
                   evaluation.WorkDevelopmentAbility >= 1 && evaluation.WorkDevelopmentAbility <= 5;
        }
    }

    // Extension method for authorization service
    public static class AuthorizationExtensions
    {
        public static Task<bool> IsManagerOfEmployeeAsync(this DepartmentAuthorizationService authService, string managerId, string employeeId)
        {
            // This would be implemented in the DepartmentAuthorizationService
            // For now, returning true as placeholder
            return Task.FromResult(true);
        }
    }
}
