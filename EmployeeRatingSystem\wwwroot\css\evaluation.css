/* Employee Evaluation Form Styles */

/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* RTL and LTR Layout Support */
.evaluation-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
}

.evaluation-container[dir="rtl"] {
    text-align: right;
}

.evaluation-container[dir="ltr"] {
    text-align: left;
}

/* Header Styles */
.evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.page-title {
    margin: 0;
    color: #2c3e50;
}

.ar-text {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
}

.en-text {
    font-size: 1.2rem;
    font-weight: 400;
    color: #6c757d;
    margin-left: 10px;
}

[dir="rtl"] .en-text {
    margin-left: 0;
    margin-right: 10px;
}

.language-toggle {
    margin-left: 20px;
}

[dir="rtl"] .language-toggle {
    margin-left: 0;
    margin-right: 20px;
}

/* Section Styles */
.evaluation-section {
    margin-bottom: 40px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.subsection-title {
    color: #495057;
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

/* Card Styles */
.employee-info-card,
.work-volume-card,
.calculated-fields-card,
.attendance-card,
.manager-evaluation-card,
.manager-totals-card,
.final-calculations-card,
.notes-card {
    padding: 25px;
    margin-bottom: 20px;
}

.employee-info-card {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
}

[dir="rtl"] .employee-info-card {
    border-left: none;
    border-right: 4px solid #28a745;
}

.work-volume-card {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

[dir="rtl"] .work-volume-card {
    border-left: none;
    border-right: 4px solid #ffc107;
}

.calculated-fields-card {
    background: #d1ecf1;
    border-left: 4px solid #17a2b8;
}

[dir="rtl"] .calculated-fields-card {
    border-left: none;
    border-right: 4px solid #17a2b8;
}

.attendance-card {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

[dir="rtl"] .attendance-card {
    border-left: none;
    border-right: 4px solid #dc3545;
}

.manager-evaluation-card {
    background: #e2e3e5;
    border-left: 4px solid #6c757d;
}

[dir="rtl"] .manager-evaluation-card {
    border-left: none;
    border-right: 4px solid #6c757d;
}

.final-calculations-card {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

[dir="rtl"] .final-calculations-card {
    border-left: none;
    border-right: 4px solid #28a745;
}

/* Form Controls */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

[dir="rtl"] .form-control {
    text-align: right;
}

.calculated-field {
    background-color: #e9ecef;
    color: #495057;
    font-weight: 500;
}

.total-score-field {
    background-color: #d4edda;
    color: #155724;
    font-weight: 700;
    font-size: 1.2rem;
    text-align: center;
}

.total-score-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #155724;
}

/* Rating Scale Styles */
.criteria-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
}

.criteria-item {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: box-shadow 0.15s ease-in-out;
}

.criteria-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rating-scale {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    justify-content: flex-start;
}

[dir="rtl"] .rating-scale {
    justify-content: flex-end;
}

.rating-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 6px;
    transition: background-color 0.15s ease-in-out;
}

.rating-option:hover {
    background-color: #f8f9fa;
}

.rating-option input[type="radio"] {
    margin-bottom: 5px;
    transform: scale(1.2);
}

.rating-number {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.rating-option input[type="radio"]:checked + .rating-number {
    color: #007bff;
    font-weight: 700;
}

/* Auto-save Indicator */
.auto-save-indicator {
    text-align: center;
    margin: 20px 0;
    padding: 10px;
    border-radius: 6px;
    background-color: #f8f9fa;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

[dir="rtl"] .action-buttons {
    flex-direction: row-reverse;
}

.btn {
    padding: 12px 24px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 50px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .evaluation-container {
        padding: 10px;
    }
    
    .evaluation-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .rating-scale {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

/* Validation Styles */
.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #28a745;
}

/* Arabic Number Support */
[dir="rtl"] .form-control[type="number"] {
    direction: ltr;
    text-align: left;
}

/* Print Styles */
@media print {
    .evaluation-container {
        box-shadow: none;
    }
    
    .action-buttons,
    .language-toggle,
    .auto-save-indicator {
        display: none;
    }
    
    .evaluation-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
