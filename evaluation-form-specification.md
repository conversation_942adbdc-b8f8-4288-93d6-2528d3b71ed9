# Employee Evaluation Form Specification

## 📋 Document Information
- **Document**: Employee Evaluation Form Design Specification
- **Technology**: C# Blazor Server
- **Languages**: Arabic (العربية) / English
- **Version**: 1.0
- **Date**: July 2025

---

## 1. 🎯 Page Overview

### 1.1 Purpose
The Employee Evaluation Form is a comprehensive bilingual interface for conducting employee performance evaluations with real-time calculations and auto-save functionality.

### 1.2 Page Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Employee Evaluation (تقييم الموظف)                   │
├─────────────────────────────────────────────────────────────┤
│ Section 1: Work Volume Metrics (مقاييس حجم العمل)            │
│ ├── Employee Information (معلومات الموظف)                   │
│ ├── Work Volume Inputs (مدخلات حجم العمل)                    │
│ ├── Auto-calculated Fields (الحقول المحسوبة تلقائياً)        │
│ └── Attendance Metrics (مقاييس الحضور)                      │
├─────────────────────────────────────────────────────────────┤
│ Section 2: Manager Evaluation (تقييم المدير)                │
│ ├── Performance Criteria (معايير الأداء) - 10 items        │
│ └── Manager Evaluation Totals (مجاميع تقييم المدير)         │
├─────────────────────────────────────────────────────────────┤
│ Final Calculations (الحسابات النهائية)                      │
├─────────────────────────────────────────────────────────────┤
│ Notes Section (قسم الملاحظات)                               │
├─────────────────────────────────────────────────────────────┤
│ Action Buttons (أزرار العمليات)                            │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. 📝 Form Fields Specification

### 2.1 Section 1: Work Volume Metrics (القسم الأول: مقاييس حجم العمل)

#### Employee Information (معلومات الموظف)
| Field | Arabic Label | English Label | Type | Properties |
|-------|-------------|---------------|------|------------|
| EmployeeName | اسم الموظف | Employee Name | Text | ReadOnly, Pre-filled |
| EmployeeId | الرقم الوظيفي | Employee ID | Text | ReadOnly, Pre-filled |
| Department | القسم | Department | Text | ReadOnly, Pre-filled |
| EvaluationPeriod | فترة التقييم | Evaluation Period | Text | ReadOnly, Format: "Month/Year" |

#### Work Volume Inputs (مدخلات حجم العمل)
| Field | Arabic Label | English Label | Type | Validation |
|-------|-------------|---------------|------|------------|
| QualityProgramWork | حجم عمل الموظف في برنامج الجودة | Work volume in Quality Program | Decimal | Min: 0, Max: 999999.99 |
| OracleProgramWork | حجم عمل الموظف في برنامج الأوراكل | Work volume in Oracle Program | Decimal | Min: 0, Max: 999999.99 |
| DocumentedWork | حجم عمل الموظف في أي عمل موثق | Work volume in documented work | Decimal | Min: 0, Max: 999999.99 |

#### Auto-calculated Fields (الحقول المحسوبة تلقائياً)
| Field | Arabic Label | English Label | Type | Calculation |
|-------|-------------|---------------|------|-------------|
| WorkPercentageInDepartment | النسبة المئوية لحجم عمل الموظف داخل القسم | Percentage of employee work within department | Percentage | Formula 1 |
| SixtyPercentScore | الدرجة 60% | Score out of 60% | Decimal | Formula 2 |

#### Attendance Metrics (مقاييس الحضور)
| Field | Arabic Label | English Label | Type | Validation |
|-------|-------------|---------------|------|------------|
| MonthlyAttendanceDays | عدد أيام الحضور في الشهر | Monthly attendance days | Integer | Min: 1, Max: 31 |
| EmployeeAttendanceDays | عدد أيام حضور الموظف | Employee attendance days | Integer | Min: 0, Max: MonthlyAttendanceDays |
| AttendancePercentage | نسبة حضور الموظف | Employee attendance percentage | Percentage | Formula 3 |
| TwentyPercentAttendanceScore | نسبة حضور الموظف في الشهر 20% | Monthly attendance percentage 20% | Decimal | Formula 4 |

### 2.2 Section 2: Manager Evaluation (القسم الثاني: تقييم المدير)

#### Performance Criteria (معايير الأداء) - 1-5 Scale Each
| Field | Arabic Label | English Label | Scale |
|-------|-------------|---------------|-------|
| WorkEfficiencyQuality | كفاءة العمل وجودته | Work efficiency and quality | 1-5 |
| LeadershipAbility | القدرة على القيادة في اتخاذ القرارات وإعطاء التوجيهات | Leadership ability in decision-making and instruction-giving | 1-5 |
| PlanningDevelopmentCreativity | قدرات التخطيط والتطوير والإبداع | Planning, development, and creativity capabilities | 1-5 |
| TeamworkParticipation | المشاركة في العمل الجماعي وتحفيز الزملاء | Teamwork participation and motivation of colleagues | 1-5 |
| ResponsibilityHandling | تحمل المسؤولية والتعامل مع ضغط العمل | Responsibility handling and work pressure management | 1-5 |
| EmergencyManagement | إدارة المواقف الطارئة | Emergency situation management | 1-5 |
| GeneralBehavior | السلوك العام والعلاقات الاجتماعية | General behavior and social relationships | 1-5 |
| SupervisorRelationship | العلاقة مع الرؤساء | Relationship with supervisors | 1-5 |
| DisciplineCompliance | الانضباط والالتزام بساعات العمل الرسمية | Discipline and official working hours compliance | 1-5 |
| WorkDevelopmentAbility | القدرة على تطوير مستوى العمل | Ability to develop work level | 1-5 |

#### Manager Evaluation Totals (مجاميع تقييم المدير)
| Field | Arabic Label | English Label | Type | Calculation |
|-------|-------------|---------------|------|-------------|
| TotalManagerScore | مجموع درجات تقييم المدير من 50 | Total manager evaluation score out of 50 | Decimal | Formula 5 |
| TwentyPercentManagerScore | تقييم المدير 20% | Manager evaluation 20% | Decimal | Formula 6 |

### 2.3 Final Calculations (الحسابات النهائية)
| Field | Arabic Label | English Label | Type | Calculation |
|-------|-------------|---------------|------|-------------|
| TotalEvaluationScore | مجموع درجات التقييم 100% | Total evaluation score 100% | Decimal | Formula 7 |

### 2.4 Notes Section (قسم الملاحظات)
| Field | Arabic Label | English Label | Type | Properties |
|-------|-------------|---------------|------|------------|
| Notes | الملاحظات | Notes | TextArea | MaxLength: 1000, Rows: 4 |

---

## 3. 🧮 Calculation Logic

### Formula 1: Department Work Percentage
```csharp
WorkPercentageInDepartment = 
    (QualityProgramWork + OracleProgramWork + DocumentedWork) ÷ 
    (DepartmentTotalQualityWork + DepartmentTotalOracleWork + DepartmentTotalDocumentedWork)
```

### Formula 2: 60% Work Score
```csharp
SixtyPercentScore = (WorkPercentageInDepartment ÷ HighestPercentageInDepartment) × 0.6
```

### Formula 3: Employee Attendance Rate
```csharp
AttendancePercentage = EmployeeAttendanceDays ÷ MonthlyAttendanceDays
```

### Formula 4: 20% Attendance Score
```csharp
TwentyPercentAttendanceScore = (AttendancePercentage ÷ HighestAttendanceRateInDepartment) × 0.2
```

### Formula 5: Manager Evaluation Score
```csharp
TotalManagerScore = WorkEfficiencyQuality + LeadershipAbility + PlanningDevelopmentCreativity + 
                   TeamworkParticipation + ResponsibilityHandling + EmergencyManagement + 
                   GeneralBehavior + SupervisorRelationship + DisciplineCompliance + 
                   WorkDevelopmentAbility
```

### Formula 6: 20% Manager Score
```csharp
TwentyPercentManagerScore = (TotalManagerScore ÷ 50) × 0.2
```

### Formula 7: Total Evaluation
```csharp
TotalEvaluationScore = SixtyPercentScore + TwentyPercentAttendanceScore + TwentyPercentManagerScore
```

---

## 4. 🌐 Arabic Interface Elements

### 4.1 RTL Layout Requirements
- **Text Direction**: `dir="rtl"` for Arabic content
- **Form Layout**: Right-to-left field alignment
- **Number Inputs**: Maintain left-to-right for numeric values
- **Buttons**: Right-to-left button order (Save on right, Cancel on left)

### 4.2 Bilingual Display Rules
- **Primary Labels**: Arabic (larger font)
- **Secondary Labels**: English (smaller font, gray)
- **Placeholders**: Arabic text
- **Validation Messages**: Bilingual (Arabic primary, English secondary)
- **Button Text**: Arabic primary with English in parentheses

### 4.3 Font and Typography
- **Arabic Font**: Noto Sans Arabic or Amiri
- **Font Size**: Arabic 14px, English 12px
- **Line Height**: 1.6 for proper Arabic text spacing
- **Text Alignment**: Right-aligned for Arabic, left-aligned for English

---

## 5. 🔄 User Interface Flow

### 5.1 Page Load Sequence
1. **Authentication Check**: Verify user has evaluation permissions
2. **Employee Data Load**: Pre-populate employee information
3. **Department Context**: Load department totals for calculations
4. **Form Initialization**: Set up real-time calculation bindings
5. **Auto-save Setup**: Initialize periodic save functionality

### 5.2 Data Entry Flow
1. **Section 1 Completion**:
   - Enter work volume data
   - Real-time calculation of percentages
   - Enter attendance data
   - Auto-calculate attendance scores

2. **Section 2 Completion**:
   - Rate each performance criterion (1-5)
   - Real-time calculation of manager totals
   - Auto-calculate final evaluation score

3. **Review and Submit**:
   - Review all calculated values
   - Add notes if needed
   - Save draft or submit final evaluation

### 5.3 Real-time Updates
- **Trigger Events**: OnChange for all input fields
- **Calculation Delay**: 500ms debounce to prevent excessive calculations
- **Visual Feedback**: Loading indicators during calculations
- **Error Handling**: Display calculation errors inline

---

## 6. ✅ Validation Rules

### 6.1 Input Validation
- **Numeric Fields**: Must be positive numbers
- **Attendance Days**: Employee days ≤ Monthly days
- **Manager Ratings**: Must be integers 1-5
- **Required Fields**: All work volume and attendance fields
- **Decimal Precision**: Maximum 2 decimal places

### 6.2 Business Logic Validation
- **Department Totals**: Must be > 0 for percentage calculations
- **Evaluation Period**: Must be current or past month
- **User Permissions**: Evaluator must have rights to evaluate this employee
- **Duplicate Prevention**: One evaluation per employee per month

### 6.3 Error Messages
```csharp
// Arabic primary, English secondary
"يجب أن يكون الرقم أكبر من صفر (Number must be greater than zero)"
"عدد أيام الحضور لا يمكن أن يتجاوز أيام الشهر (Attendance days cannot exceed monthly days)"
"يجب اختيار تقييم من 1 إلى 5 (Rating must be between 1 and 5)"
```

---

## 7. 💾 Auto-save Functionality

### 7.1 Auto-save Triggers
- **Time-based**: Every 30 seconds if changes detected
- **Field-based**: After completing each section
- **Navigation**: Before leaving page or closing browser
- **Manual**: Save draft button click

### 7.2 Save States
- **Draft**: Incomplete evaluation, can be edited
- **Submitted**: Complete evaluation, read-only
- **Under Review**: Submitted evaluation pending approval

### 7.3 Recovery Features
- **Session Recovery**: Restore unsaved changes on page reload
- **Conflict Resolution**: Handle concurrent edits
- **Version History**: Track evaluation changes over time

---

## 8. 🎨 UI Components

### 8.1 Input Components
- **Numeric Input**: Custom component with Arabic number support
- **Rating Scale**: 1-5 star or button selection
- **Text Area**: RTL-aware multi-line input
- **Read-only Display**: Styled calculated fields

### 8.2 Layout Components
- **Section Cards**: Collapsible sections with progress indicators
- **Calculation Panel**: Real-time display of computed values
- **Progress Bar**: Evaluation completion percentage
- **Action Bar**: Fixed bottom bar with save/submit buttons

### 8.3 Feedback Components
- **Loading Indicators**: Calculation in progress
- **Success Messages**: Save confirmation
- **Error Alerts**: Validation failures
- **Info Tooltips**: Field explanations in both languages

---

## 9. 🔐 Security and Access Control

### 9.1 Authorization Rules
- **Evaluator Rights**: Must be Manager or Supervisor with access to employee
- **Department Scope**: Can only evaluate employees in accessible departments
- **Time Restrictions**: Evaluations only for current/past evaluation periods
- **Edit Permissions**: Only draft evaluations can be modified

### 9.2 Data Protection
- **Input Sanitization**: All text inputs sanitized for XSS prevention
- **CSRF Protection**: Anti-forgery tokens on all form submissions
- **Audit Logging**: Track all evaluation actions and changes
- **Session Validation**: Verify user session on each auto-save

---

## 10. 📱 Responsive Design

### 10.1 Mobile Considerations
- **Touch-friendly**: Larger touch targets for mobile devices
- **Keyboard Support**: Numeric keypad for number inputs
- **Scroll Optimization**: Smooth scrolling between sections
- **Orientation**: Support both portrait and landscape modes

### 10.2 Accessibility
- **Screen Readers**: ARIA labels for all form elements
- **Keyboard Navigation**: Tab order follows logical flow
- **High Contrast**: Support for high contrast mode
- **Font Scaling**: Responsive to browser font size settings

---

## 11. 🧪 Testing Scenarios

### 11.1 Functional Testing
- **Calculation Accuracy**: Verify all 7 formulas produce correct results
- **Real-time Updates**: Test immediate calculation updates
- **Auto-save**: Verify draft saving and recovery
- **Validation**: Test all input validation rules

### 11.2 Bilingual Testing
- **RTL Layout**: Verify proper right-to-left display
- **Font Rendering**: Test Arabic font display across browsers
- **Mixed Content**: Test forms with both Arabic and English text
- **Number Formatting**: Verify Arabic vs Western numerals

### 11.3 Performance Testing
- **Calculation Speed**: Ensure calculations complete within 100ms
- **Auto-save Performance**: Test with large datasets
- **Concurrent Users**: Multiple evaluators editing simultaneously
- **Browser Compatibility**: Test across major browsers

---

## 12. 📊 Data Flow Diagram

```
User Input → Validation → Real-time Calculation → UI Update → Auto-save
     ↓              ↓              ↓              ↓           ↓
Field Change → Business Rules → Formula Engine → Display → Database
     ↓              ↓              ↓              ↓           ↓
Trigger Event → Error Check → Department Data → Feedback → Audit Log
```

---

## 13. 🎯 Success Criteria

### 13.1 User Experience
- **Form Completion Time**: Average 5-10 minutes per evaluation
- **Error Rate**: Less than 2% validation errors
- **Auto-save Success**: 99.9% successful auto-saves
- **Mobile Usability**: Fully functional on tablets and phones

### 13.2 Technical Performance
- **Page Load Time**: Under 2 seconds
- **Calculation Response**: Under 100ms for all formulas
- **Data Accuracy**: 100% calculation accuracy
- **Uptime**: 99.9% form availability

---

**Document Version**: 1.0
**Last Updated**: July 2025
**Next Review**: Implementation Phase
