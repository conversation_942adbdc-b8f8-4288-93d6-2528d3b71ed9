# Product Requirements Document: Employee Performance Evaluation System

## 📋 Document Information
- **Product Name**: Employee Performance Evaluation System (Bilingual Arabic/English)
- **Version**: 3.0 - C# Blazor Enterprise Edition
- **Date**: July 2025
- **Document Owner**: Development Team
- **Languages**: Arabic (العربية) / English
- **Technology Stack**: C# with Blazor Server

---

## 1. 🎯 Product Overview

### 1.1 Vision Statement
Create a comprehensive, multilingual digital employee evaluation system using C# and Blazor Server that enables hierarchical performance management across complex organizational structures, with role-based access control and configurable evaluation criteria to drive organizational excellence through data-driven performance management.

### 1.2 Product Goals
- Implement hierarchical department-based performance management using C# and Blazor
- Provide role-based access control with strict security boundaries
- Enable configurable, bilingual evaluation criteria (Arabic/English)
- Support unlimited organizational depth with many-to-many relationships
- Automate performance tracking with weighted scoring systems including specific work volume metrics
- Ensure data security and access control across department hierarchies
- Implement comprehensive employee evaluation forms with Arabic interface support



---

## 2. 👥 User Roles & Hierarchical Access Control

### 2.1 Primary User Roles

#### **Super Admin (مدير النظام الرئيسي)**
- **Role**: Complete system administration and configuration
- **Access Level**: Full system access across all departments and hierarchies
- **Key Permissions**:
  - Complete user management (create, edit, delete, assign roles)
  - Department hierarchy management (create, modify, delete departments and sub-departments)
  - Manager assignment to departments at any level
  - Database upload and system configuration
  - Rating criteria configuration (add/remove questions, adjust category percentages)
  - System-wide reporting and analytics
  - Audit trail access and compliance monitoring
- **Key Needs**: System configuration, organizational structure management, compliance oversight

#### **Manager/Department Head (مدير القسم)**
- **Role**: Department-level performance management and oversight
- **Access Level**: Full access to assigned department hierarchy (including all sub-departments)
- **Key Permissions**:
  - View and manage all supervisors within their department tree
  - Access to all employees under their department hierarchy
  - Evaluate direct report supervisors
  - Generate department-wide reports and analytics
  - Assign supervisors to sub-departments within their hierarchy
  - View performance trends across their entire department tree
- **Access Restrictions**: Cannot access other departments outside their assigned hierarchy
- **Key Needs**: Department performance oversight, supervisor management, hierarchical reporting

#### **Supervisor/Team Lead (مشرف الفريق)**
- **Role**: Direct team management and employee evaluation
- **Access Level**: Limited to direct report employees within assigned team/sub-department
- **Key Permissions**:
  - View and evaluate only direct report employees
  - Generate team-specific reports
  - Track team performance metrics
  - Submit evaluations for approval workflow
- **Access Restrictions**: Cannot see employees from other supervisors or departments
- **Key Needs**: Efficient team evaluation, performance tracking, direct report management

#### **Quality Team (فريق الجودة)**
- **Role**: Quality assurance and evaluation oversight
- **Access Level**: Cross-departmental quality metrics access (read-only)
- **Key Permissions**:
  - View evaluation quality metrics across departments
  - Access audit trails for evaluation processes
  - Generate quality compliance reports
  - Monitor evaluation completion rates
  - Review evaluation consistency and standards
- **Access Restrictions**: Read-only access, cannot modify evaluations or user data
- **Key Needs**: Quality monitoring, compliance tracking, evaluation standards oversight

#### **Employee (الموظف)**
- **Role**: View personal performance and development tracking
- **Access Level**: Personal performance data and feedback only
- **Key Permissions**:
  - View personal evaluation history
  - Track individual performance trends
  - Access development recommendations
  - View recognition and achievements
- **Key Needs**: Transparent feedback, performance tracking, goal visibility

### 2.2 Hierarchical Access Control Rules

#### **Department Hierarchy Access**
- **Many-to-Many Relationships**: Users can belong to multiple departments
- **Inheritance Rules**: Higher-level roles inherit access to all subordinate levels
- **Strict Boundaries**: No cross-department access outside assigned hierarchies
- **Dynamic Updates**: Access rights update automatically when organizational structure changes

#### **Data Visibility Matrix**
| Role | Own Data | Direct Reports | Department Tree | Cross-Department | System Config |
|------|----------|----------------|-----------------|------------------|---------------|
| Super Admin | ✅ | ✅ | ✅ | ✅ | ✅ |
| Manager | ✅ | ✅ | ✅ | ❌ | ❌ |
| Supervisor | ✅ | ✅ | ❌ | ❌ | ❌ |
| Quality Team | ❌ | ❌ | ✅ (Read-only) | ✅ (Read-only) | ❌ |
| Employee | ✅ | ❌ | ❌ | ❌ | ❌ |

---

## 3. 🏢 Department Hierarchy & Organizational Structure

### 3.1 Department Hierarchy Requirements

#### **Unlimited Nested Hierarchy Support**
- **Infinite Depth**: Support unlimited levels of department nesting
  - Example: Company → Division → Department → Sub-Department → Team → Sub-Team
- **Many-to-Many Relationships**: Employees can belong to multiple departments
- **Cross-Functional Teams**: Support matrix organizational structures
- **Dynamic Restructuring**: Real-time organizational changes without data loss

#### **Hierarchy Examples**
```
Company (الشركة)
├── Sales Division (قسم المبيعات)
│   ├── Regional Sales (المبيعات الإقليمية)
│   │   ├── North Region Team (فريق المنطقة الشمالية)
│   │   └── South Region Team (فريق المنطقة الجنوبية)
│   └── Corporate Sales (مبيعات الشركات)
├── Operations Division (قسم العمليات)
│   ├── Quality Assurance (ضمان الجودة)
│   └── Production (الإنتاج)
│       ├── Manufacturing (التصنيع)
│       └── Assembly (التجميع)
└── Support Division (قسم الدعم)
    ├── IT Department (قسم تقنية المعلومات)
    └── HR Department (قسم الموارد البشرية)
```

#### **Access Control Rules**
- **Hierarchical Inheritance**: Access to all subordinate levels
- **Strict Boundaries**: No access outside assigned hierarchy
- **Manager Assignment**: Each department level can have assigned managers
- **Supervisor Distribution**: Supervisors assigned to specific sub-departments

### 3.2 Organizational Visualization

#### **Hierarchy Visualization Features**
- **Interactive Org Chart**: Clickable, expandable organizational tree
- **Department Cards**: Show department info, manager, employee count
- **Access Indicators**: Visual indicators of user's access scope
- **Search & Filter**: Find departments, managers, employees quickly
- **Breadcrumb Navigation**: Clear path showing current location in hierarchy

#### **Role-Based Views**
- **Super Admin**: Complete organizational tree with edit capabilities
- **Manager**: Their department tree with management tools
- **Supervisor**: Their team/sub-department with limited scope
- **Quality Team**: Read-only view across all departments
- **Employee**: Personal position in organizational structure

### 3.3 Department Management Features

#### **Super Admin Capabilities**
- Create/modify/delete departments at any level
- Assign managers to departments
- Move departments within hierarchy
- Merge or split departments
- Bulk organizational changes
- Historical change tracking

#### **Manager Capabilities**
- Create sub-departments within their hierarchy
- Assign supervisors to sub-departments
- Modify department information within their scope
- Request organizational changes to Super Admin

---

## 4. 🔧 Core Features & Requirements

### 4.1 Employee-Department Mapping System

#### **Functional Requirements**
- Many-to-many employee-department relationships
- Primary department designation for each employee
- Secondary department affiliations
- Automated assignment based on organizational rules
- Real-time updates when reporting relationships change
- Historical tracking of all organizational changes

#### **Technical Requirements**
- Django models for complex organizational hierarchy
- Many-to-many relationships with through tables
- Audit trail for all relationship changes
- API endpoints for organizational chart visualization
- Caching layer for performance optimization

### 4.2 Comprehensive Employee Evaluation Form Structure (هيكل نموذج تقييم الموظف الشامل)

#### **Section 1: Work Volume Metrics (القسم الأول: مقاييس حجم العمل)**

**Employee Information (معلومات الموظف)**:
- **Employee Name (اسم الموظف)**: Full name in Arabic and English
- **Employee ID (الرقم الوظيفي)**: Unique identifier
- **Department (القسم)**: Current department assignment
- **Evaluation Period (فترة التقييم)**: Month/Year being evaluated

**Work Volume Calculations (حسابات حجم العمل)**:
- **Work volume in Quality Program (حجم عمل الموظف في برنامج الجودة)**: Numeric input
- **Work volume in Oracle Program (حجم عمل الموظف في برنامج الأوراكل)**: Numeric input
- **Work volume in any documented work (حجم عمل الموظف في أي عمل موثق)**: Numeric input
- **Department totals**: System calculates total work across all employees in department
- **Percentage of employee work within department (النسبة المئوية لحجم عمل الموظف داخل القسم)**: Auto-calculated
- **Score out of 60% (الدرجة 60%)**: Auto-calculated based on highest percentage in department

**Attendance Metrics (مقاييس الحضور)**:
- **Monthly attendance days (عدد أيام الحضور في الشهر)**: Total working days in month
- **Employee attendance days (عدد أيام حضور الموظف)**: Actual days attended
- **Employee attendance percentage (نسبة حضور الموظف)**: Auto-calculated
- **Monthly attendance percentage 20% (نسبة حضور الموظف في الشهر 20%)**: Auto-calculated score

**Section 1 Totals**:
- **Total evaluation score 100% (مجموع درجات التقييم 100%)**: Sum of 60% work score + 20% attendance score + 20% manager evaluation
- **Notes (الملاحظات)**: Free text field for additional comments

#### **Section 2: Performance Criteria - Manager Evaluation (القسم الثاني: معايير الأداء - تقييم المدير)**

**Performance Criteria (معايير الأداء)** - Each rated 1-5 scale:
1. **Work efficiency and quality (كفاءة العمل وجودته)**
2. **Leadership ability in decision-making and instruction-giving (القدرة على القيادة في اتخاذ القرارات وإعطاء التوجيهات)**
3. **Planning, development, and creativity capabilities (قدرات التخطيط والتطوير والإبداع)**
4. **Teamwork participation and motivation of colleagues (المشاركة في العمل الجماعي وتحفيز الزملاء)**
5. **Responsibility handling and work pressure management (تحمل المسؤولية والتعامل مع ضغط العمل)**
6. **Emergency situation management (إدارة المواقف الطارئة)**
7. **General behavior and social relationships (السلوك العام والعلاقات الاجتماعية)**
8. **Relationship with supervisors (العلاقة مع الرؤساء)**
9. **Discipline and official working hours compliance (الانضباط والالتزام بساعات العمل الرسمية)**
10. **Ability to develop work level (القدرة على تطوير مستوى العمل)**

**Manager Evaluation Totals**:
- **Total manager evaluation score out of 50 (مجموع درجات تقييم المدير من 50)**
- **Manager evaluation 20% (تقييم المدير 20%)**: Calculated as (Total score ÷ 50) × 0.2

### 4.3 Calculation Formulas Implementation (تنفيذ صيغ الحساب)

#### **Core Calculation Formulas**
The system must implement these specific calculation formulas:

1. **Department work percentage (نسبة العمل في القسم)**:
   ```
   Employee Percentage = (Employee Quality Work + Employee Oracle Work + Employee Documented Work) ÷
                        (Department Total Quality Work + Department Total Oracle Work + Department Total Documented Work)
   ```

2. **60% Work Score (درجة العمل 60%)**:
   ```
   60% Score = (Employee Percentage ÷ Highest Percentage in Department) × 0.6
   ```

3. **Employee attendance rate (معدل حضور الموظف)**:
   ```
   Attendance Rate = Employee Attendance Days ÷ Monthly Attendance Days
   ```

4. **20% Attendance score (درجة الحضور 20%)**:
   ```
   20% Attendance Score = (Employee Attendance Rate ÷ Highest Attendance Rate in Department) × 0.2
   ```

5. **Manager evaluation score (درجة تقييم المدير)**:
   ```
   Manager Score = Sum of all 10 criteria scores (each 1-5)
   20% Manager Score = (Total Criteria Score ÷ 50) × 0.2
   ```

6. **Total evaluation (التقييم الإجمالي)**:
   ```
   Total Evaluation = 60% Work Score + 20% Attendance Score + 20% Manager Score
   ```

#### **Real-time Calculation Features**
- **Auto-calculation**: All formulas calculated automatically when data is entered
- **Department-wide updates**: When any employee data changes, all related percentages recalculate
- **Validation**: Ensure all inputs are valid before calculations
- **Audit trail**: Track all calculation changes and updates



---

## 5. 🏗️ Technical Architecture Specifications

### 5.1 Technology Stack

#### **Core Technologies**
- **Programming Language**: C# (.NET 8)
- **Framework**: Blazor Server
- **Database**: SQL Server (recommended) or PostgreSQL
- **ORM**: Entity Framework Core
- **Authentication**: ASP.NET Core Identity
- **UI Framework**: Bootstrap 5 with RTL support
- **Localization**: ASP.NET Core Localization with Arabic/English support

#### **Blazor Server Architecture Benefits**
- **Real-time updates**: SignalR integration for live calculation updates
- **Server-side rendering**: Better performance for complex calculations
- **Security**: Business logic remains on server
- **Arabic support**: Better RTL text handling on server-side
- **State management**: Easier to maintain complex evaluation state

### 5.2 Database Schema Design

#### **Core Entity Models (C# Entity Framework)**

**User Model (Extended Identity User)**
```csharp
public class ApplicationUser : IdentityUser
{
    public string EmployeeId { get; set; }
    public string ArabicName { get; set; }
    public string EnglishName { get; set; }
    public int PrimaryDepartmentId { get; set; }
    public Department PrimaryDepartment { get; set; }
    public ICollection<UserDepartment> UserDepartments { get; set; }
    public UserRole Role { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

**Department Hierarchy Model**
```csharp
public class Department
{
    public int Id { get; set; }
    public string NameEn { get; set; }
    public string NameAr { get; set; }
    public string Code { get; set; }
    public int? ParentId { get; set; }
    public Department Parent { get; set; }
    public ICollection<Department> Children { get; set; }
    public ICollection<ApplicationUser> Managers { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
}
```

**Employee Evaluation Model**
```csharp
public class EmployeeEvaluation
{
    public int Id { get; set; }
    public string EmployeeId { get; set; }
    public ApplicationUser Employee { get; set; }
    public int EvaluationPeriodMonth { get; set; }
    public int EvaluationPeriodYear { get; set; }

    // Section 1: Work Volume Metrics
    public decimal QualityProgramWork { get; set; }
    public decimal OracleProgramWork { get; set; }
    public decimal DocumentedWork { get; set; }
    public decimal WorkPercentageInDepartment { get; set; }
    public decimal SixtyPercentScore { get; set; }

    // Attendance Metrics
    public int MonthlyAttendanceDays { get; set; }
    public int EmployeeAttendanceDays { get; set; }
    public decimal AttendancePercentage { get; set; }
    public decimal TwentyPercentAttendanceScore { get; set; }

    // Section 2: Manager Evaluation Criteria (1-5 scale each)
    public int WorkEfficiencyQuality { get; set; }
    public int LeadershipAbility { get; set; }
    public int PlanningDevelopmentCreativity { get; set; }
    public int TeamworkParticipation { get; set; }
    public int ResponsibilityHandling { get; set; }
    public int EmergencyManagement { get; set; }
    public int GeneralBehavior { get; set; }
    public int SupervisorRelationship { get; set; }
    public int DisciplineCompliance { get; set; }
    public int WorkDevelopmentAbility { get; set; }

    public decimal TotalManagerScore { get; set; }
    public decimal TwentyPercentManagerScore { get; set; }
    public decimal TotalEvaluationScore { get; set; }

    public string Notes { get; set; }
    public string EvaluatorId { get; set; }
    public ApplicationUser Evaluator { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

### 5.3 Blazor Components Architecture

#### **Core Blazor Components**

**Evaluation Form Component**
```csharp
@page "/evaluation/{employeeId}"
@using Microsoft.AspNetCore.Localization
@inject IStringLocalizer<EvaluationForm> Localizer
@inject EvaluationService EvaluationService

<div class="evaluation-form" dir="@(CultureInfo.CurrentCulture.TextInfo.IsRightToLeft ? "rtl" : "ltr")">
    <h2>@Localizer["EmployeeEvaluation"]</h2>

    <!-- Section 1: Work Volume Metrics -->
    <div class="section-1">
        <h3>@Localizer["WorkVolumeMetrics"]</h3>
        <EditForm Model="@evaluation" OnValidSubmit="@HandleValidSubmit">
            <DataAnnotationsValidator />

            <!-- Employee Information -->
            <div class="employee-info">
                <InputText @bind-Value="evaluation.Employee.ArabicName"
                          placeholder="@Localizer["EmployeeName"]" readonly />
                <InputText @bind-Value="evaluation.Employee.EmployeeId"
                          placeholder="@Localizer["EmployeeId"]" readonly />
            </div>

            <!-- Work Volume Inputs -->
            <div class="work-volume">
                <InputNumber @bind-Value="evaluation.QualityProgramWork"
                           @onchange="RecalculateScores"
                           placeholder="@Localizer["QualityProgramWork"]" />
                <InputNumber @bind-Value="evaluation.OracleProgramWork"
                           @onchange="RecalculateScores"
                           placeholder="@Localizer["OracleProgramWork"]" />
                <InputNumber @bind-Value="evaluation.DocumentedWork"
                           @onchange="RecalculateScores"
                           placeholder="@Localizer["DocumentedWork"]" />
            </div>

            <!-- Auto-calculated fields -->
            <div class="calculated-fields">
                <span>@Localizer["WorkPercentage"]: @evaluation.WorkPercentageInDepartment.ToString("P2")</span>
                <span>@Localizer["SixtyPercentScore"]: @evaluation.SixtyPercentScore.ToString("F2")</span>
            </div>
        </EditForm>
    </div>

    <!-- Section 2: Manager Evaluation -->
    <div class="section-2">
        <h3>@Localizer["ManagerEvaluation"]</h3>
        <!-- Performance criteria inputs (1-5 scale) -->
    </div>
</div>

@code {
    [Parameter] public string EmployeeId { get; set; }
    private EmployeeEvaluation evaluation = new();

    private async Task RecalculateScores()
    {
        await EvaluationService.RecalculateScores(evaluation);
        StateHasChanged();
    }
}
```

#### **Arabic/RTL Support Implementation**
```csharp
// Startup.cs configuration
public void ConfigureServices(IServiceCollection services)
{
    services.Configure<RequestLocalizationOptions>(options =>
    {
        var supportedCultures = new[] { "en-US", "ar-SA" };
        options.SetDefaultCulture(supportedCultures[0])
               .AddSupportedCultures(supportedCultures)
               .AddSupportedUICultures(supportedCultures);
    });

    services.AddLocalization(options => options.ResourcesPath = "Resources");
}

// CSS for RTL support
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .form-control {
    text-align: right;
}

.rtl .btn {
    margin-left: 0;
    margin-right: 0.5rem;
}
```

### 5.4 Access Control Implementation

#### **Role-Based Authorization in C#**
```csharp
public class DepartmentAuthorizationService
{
    public bool HasDepartmentAccess(ApplicationUser user, Department department)
    {
        return user.Role switch
        {
            UserRole.SuperAdmin => true,
            UserRole.Manager => user.GetManagedHierarchy().Contains(department),
            UserRole.Supervisor => department.Id == user.PrimaryDepartmentId,
            UserRole.QualityTeam => true, // Read-only access
            _ => false
        };
    }

    public bool CanEvaluateUser(ApplicationUser evaluator, ApplicationUser targetUser)
    {
        return evaluator.Role switch
        {
            UserRole.SuperAdmin => true,
            UserRole.Manager => evaluator.GetManagedHierarchy()
                .Any(d => d.Id == targetUser.PrimaryDepartmentId),
            UserRole.Supervisor => evaluator.GetDirectReports().Contains(targetUser),
            _ => false
        };
    }
}
```

#### **Blazor Authorization Components**
```csharp
@attribute [Authorize(Roles = "SuperAdmin,Manager,Supervisor")]
@inject DepartmentAuthorizationService AuthService

@if (AuthService.CanEvaluateUser(CurrentUser, Employee))
{
    <!-- Evaluation form content -->
}
else
{
    <div class="alert alert-danger">
        @Localizer["UnauthorizedAccess"]
    </div>
}
```

---

## 6. 💻 Blazor Implementation Requirements

### 6.1 Role-Based Dashboard Components

#### **Super Admin Dashboard (لوحة تحكم المدير الرئيسي)**
- Complete system overview with all departments using Blazor Server components
- User management interface with real-time updates via SignalR
- Department hierarchy management with drag-drop functionality
- Evaluation criteria configuration panel with live preview
- System-wide performance metrics using Chart.js integration
- Audit trail and compliance monitoring with real-time notifications
- Data import/export functionality with progress indicators

#### **Manager Dashboard (لوحة تحكم مدير القسم)**
- Interactive department hierarchy tree using Blazor TreeView component
- Real-time employee status updates across department tree
- Pending evaluations tracker with automatic refresh
- Department-wide performance comparisons with interactive charts
- Team analytics dashboard with drill-down capabilities
- Supervisor assignment interface with validation
- Department-specific reporting with export functionality

#### **Supervisor Dashboard (لوحة تحكم المشرف)**
- Direct report team overview with card-based layout
- Individual employee evaluation forms with auto-save
- Pending evaluations list with priority indicators
- Team performance trends using responsive charts
- Quick evaluation access with modal dialogs
- Team goals tracking with progress bars

#### **Quality Team Dashboard (لوحة تحكم فريق الجودة)**
- Cross-departmental metrics with read-only access controls
- Evaluation completion rates with real-time updates
- Quality compliance monitoring with alert system
- Evaluation consistency analysis with statistical views
- Audit trail access with advanced filtering
- Standards compliance tracking with dashboard widgets

#### **Employee Dashboard (لوحة تحكم الموظف)**
- Personal performance scorecard with bilingual support
- Historical performance trends with interactive timeline
- Individual goal tracking with achievement badges
- Feedback history with expandable sections
- Recognition achievements with visual indicators
- Organizational position view with hierarchy navigation

### 6.2 Blazor Evaluation Interface

#### **Core Blazor Features**
- Server-side Blazor components for complex evaluation forms
- Real-time score calculation using SignalR
- Rich text editor integration (TinyMCE or similar)
- Auto-save functionality with periodic state persistence
- Evaluation workflow with step-by-step guidance
- Responsive design using Bootstrap 5 with RTL support

#### **Technical Implementation**
```csharp
// Real-time calculation service
public class EvaluationCalculationService
{
    public async Task<decimal> CalculateWorkPercentage(EmployeeEvaluation evaluation)
    {
        var departmentTotals = await GetDepartmentTotals(evaluation.Employee.PrimaryDepartmentId);
        var employeeTotal = evaluation.QualityProgramWork + evaluation.OracleProgramWork + evaluation.DocumentedWork;
        return employeeTotal / departmentTotals.TotalWork;
    }

    public async Task<decimal> CalculateSixtyPercentScore(EmployeeEvaluation evaluation)
    {
        var workPercentage = await CalculateWorkPercentage(evaluation);
        var highestPercentage = await GetHighestPercentageInDepartment(evaluation.Employee.PrimaryDepartmentId);
        return (workPercentage / highestPercentage) * 0.6m;
    }
}

// SignalR Hub for real-time updates
public class EvaluationHub : Hub
{
    public async Task JoinEvaluationGroup(string evaluationId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"evaluation-{evaluationId}");
    }

    public async Task UpdateCalculation(string evaluationId, decimal newScore)
    {
        await Clients.Group($"evaluation-{evaluationId}").SendAsync("ScoreUpdated", newScore);
    }
}
```

#### **Arabic Text Support**
- RTL layout support using CSS Grid and Flexbox
- Arabic font integration (Noto Sans Arabic, Amiri)
- Bidirectional text handling for mixed Arabic/English content
- Localized number formatting for Arabic numerals
- Date formatting supporting both Gregorian and Hijri calendars

---

## 7. 📋 User Stories & Acceptance Criteria

### 7.1 Super Admin User Stories

#### **Story 1: Department Hierarchy Management**
**As a** Super Admin
**I want to** create and manage unlimited nested department hierarchies
**So that** I can accurately represent the organization's complex structure

**Acceptance Criteria:**
- ✅ Can create departments with unlimited nesting levels
- ✅ Can assign Arabic and English names to each department
- ✅ Can move departments within the hierarchy without data loss
- ✅ Can assign managers to any department level
- ✅ Can view complete organizational tree with visual indicators
- ✅ Changes are logged in audit trail with timestamps

#### **Story 2: User Role Management**
**As a** Super Admin
**I want to** create users and assign them to multiple departments with specific roles
**So that** I can control access based on organizational hierarchy

**Acceptance Criteria:**
- ✅ Can create users with bilingual names (Arabic/English)
- ✅ Can assign users to multiple departments with different roles
- ✅ Can designate primary department for each user
- ✅ Can modify user roles and department assignments
- ✅ Access rights update automatically when assignments change
- ✅ Cannot delete users with active evaluations (soft delete only)

#### **Story 3: Evaluation Criteria Configuration**
**As a** Super Admin
**I want to** configure evaluation categories and questions dynamically
**So that** the system adapts to changing organizational needs

**Acceptance Criteria:**
- ✅ Can add/edit/remove evaluation categories (except mandatory Attendance)
- ✅ Can adjust percentage weights for each category (must total 100%)
- ✅ Can add/edit/remove questions within categories
- ✅ Can maintain bilingual content (Arabic/English) for all criteria
- ✅ Changes apply to future evaluations without affecting completed ones
- ✅ System validates weight totals and prevents invalid configurations

### 7.2 Manager User Stories

#### **Story 4: Department Hierarchy Access**
**As a** Manager
**I want to** view and manage all employees within my department hierarchy
**So that** I can oversee performance across my entire organizational tree

**Acceptance Criteria:**
- ✅ Can view all sub-departments and employees within assigned hierarchy
- ✅ Cannot access employees or data from other department hierarchies
- ✅ Can see performance metrics for entire department tree
- ✅ Can assign supervisors to sub-departments within hierarchy
- ✅ Can generate reports for entire department tree
- ✅ Access automatically updates when department structure changes

#### **Story 5: Supervisor Management**
**As a** Manager
**I want to** evaluate and manage supervisors within my department
**So that** I can ensure effective team leadership at all levels

**Acceptance Criteria:**
- ✅ Can evaluate direct report supervisors
- ✅ Can view supervisor performance and their team metrics
- ✅ Can reassign supervisors within department hierarchy
- ✅ Can access supervisor evaluation history and trends
- ✅ Can compare supervisor performance across department
- ✅ Cannot modify evaluations completed by supervisors

### 7.3 Supervisor User Stories

#### **Story 6: Team Evaluation**
**As a** Supervisor
**I want to** evaluate only my direct report employees
**So that** I can provide focused, accurate performance assessments

**Acceptance Criteria:**
- ✅ Can only see and evaluate direct report employees
- ✅ Cannot access employees from other teams or supervisors
- ✅ Can complete evaluations using configured criteria and weights
- ✅ Can save draft evaluations and complete them later
- ✅ Can view team performance trends and analytics
- ✅ Evaluations require approval workflow before finalization

### 7.4 Quality Team User Stories

#### **Story 7: Quality Monitoring**
**As a** Quality Team member
**I want to** monitor evaluation quality across all departments
**So that** I can ensure consistent evaluation standards

**Acceptance Criteria:**
- ✅ Can view evaluation completion rates across all departments (read-only)
- ✅ Can access audit trails for evaluation processes
- ✅ Can generate quality compliance reports
- ✅ Can monitor evaluation consistency and identify outliers
- ✅ Cannot modify any evaluation data or user information
- ✅ Can export quality metrics for analysis

### 7.5 Employee User Stories

#### **Story 8: Performance Visibility**
**As an** Employee
**I want to** view my performance evaluations and progress
**So that** I can understand my performance and development areas

**Acceptance Criteria:**
- ✅ Can view personal evaluation history in preferred language
- ✅ Can see performance trends and progress over time
- ✅ Can access feedback and development recommendations
- ✅ Can view position within organizational hierarchy
- ✅ Cannot access other employees' evaluation data
- ✅ Can download personal performance reports

---

## 8. 📊 Reports & Analytics

### 8.1 Hierarchical Reports & Analytics

#### **Individual Reports (التقارير الفردية)**
- **Personal Performance Scorecard**: Bilingual detailed performance breakdown
- **Performance Trend Analysis**: Historical performance tracking with visual charts
- **Goal Achievement Tracking**: Progress against set objectives and KPIs
- **Competency Gap Analysis**: Skills assessment and development areas
- **Development Plan Recommendations**: Personalized improvement suggestions
- **Attendance Analytics**: Detailed attendance patterns and compliance metrics

#### **Team/Department Reports (تقارير الفريق/القسم)**
- **Hierarchical Performance Summary**: Performance across department tree levels
- **Team Member Comparisons**: Comparative analysis within teams and departments
- **Department Goal Achievement**: Collective performance against department objectives
- **Performance Distribution Analysis**: Statistical distribution of ratings across hierarchy
- **Supervisor Effectiveness Reports**: Analysis of supervisor performance and team outcomes
- **Cross-Department Comparisons**: Performance benchmarking (for authorized roles)

#### **Organizational Reports (التقارير التنظيمية)**
- **Company-wide Performance Trends**: Overall organizational performance patterns
- **Top Performer Identification**: Recognition-eligible employees across all levels
- **Department Hierarchy Analytics**: Performance analysis by organizational structure
- **Evaluation Completion Tracking**: Compliance monitoring across all departments
- **Quality Assurance Reports**: Evaluation consistency and standards compliance
- **ROI Analysis**: Performance initiative effectiveness and impact measurement

### 8.2 Blazor Analytics & Reporting Implementation

#### **Advanced Analytics Features using Blazor**
- **Interactive Report Builder**: Blazor components with drag-drop functionality
- **Custom Date Range Selection**: Blazor DatePicker components with Arabic calendar support
- **Multiple Visualization Options**: Chart.js integration with RTL support for Arabic labels
- **Role-Based Data Filtering**: Server-side filtering with Blazor authorization policies
- **Export Functionality**: PDF and Excel export with Arabic support

#### **Blazor Bilingual Support Implementation**
```csharp
// Localization service for Blazor components
public class BlazorLocalizationService
{
    public string GetLocalizedText(string key, CultureInfo culture)
    {
        return culture.Name switch
        {
            "ar-SA" => GetArabicText(key),
            "en-US" => GetEnglishText(key),
            _ => GetEnglishText(key)
        };
    }

    public string FormatNumber(decimal number, CultureInfo culture)
    {
        return culture.Name == "ar-SA"
            ? number.ToString("N2", new CultureInfo("ar-SA"))
            : number.ToString("N2", new CultureInfo("en-US"));
    }
}

// RTL/LTR layout component
@inject IJSRuntime JSRuntime

<div class="report-container" dir="@TextDirection">
    <style>
        .report-container[dir="rtl"] {
            text-align: right;
        }
        .report-container[dir="rtl"] .chart-container {
            direction: ltr; /* Charts remain LTR for readability */
        }
    </style>
    @ChildContent
</div>

@code {
    [Parameter] public RenderFragment ChildContent { get; set; }
    private string TextDirection => CultureInfo.CurrentCulture.TextInfo.IsRightToLeft ? "rtl" : "ltr";
}
```

#### **Access-Controlled Analytics with Blazor**
- **Hierarchical Filtering**: Blazor components with automatic data filtering based on user claims
- **Role-Based Metrics**: Dynamic component rendering based on user roles
- **Department Tree Analytics**: Interactive tree components with drill-down navigation
- **Comparative Analysis**: Blazor charts with department-scoped data comparison
- **Real-time Updates**: SignalR integration for live analytics updates

---

## 9. 🔒 Security & Compliance

### 9.1 Blazor Security Implementation

#### **ASP.NET Core Identity Integration**
```csharp
// Startup.cs security configuration
public void ConfigureServices(IServiceCollection services)
{
    services.AddDefaultIdentity<ApplicationUser>(options =>
    {
        options.Password.RequireDigit = true;
        options.Password.RequiredLength = 8;
        options.Password.RequireNonAlphanumeric = true;
        options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
        options.Lockout.MaxFailedAccessAttempts = 5;
    })
    .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>();

    services.AddAuthorization(options =>
    {
        options.AddPolicy("SuperAdminOnly", policy => policy.RequireRole("SuperAdmin"));
        options.AddPolicy("ManagerOrAbove", policy => policy.RequireRole("SuperAdmin", "Manager"));
        options.AddPolicy("EvaluatorRoles", policy => policy.RequireRole("SuperAdmin", "Manager", "Supervisor"));
    });
}
```

#### **Security Features**
- **Role-based access control (RBAC)**: ASP.NET Core Identity with custom roles
- **Session management**: Configurable timeout with automatic logout
- **Data encryption**: Entity Framework encryption for sensitive data
- **CSRF protection**: Built-in ASP.NET Core anti-forgery tokens













